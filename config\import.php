<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Import Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for optimizing large data imports
    |
    */

    // Global import settings
    'enable_logging' => env('IMPORT_ENABLE_LOGGING', true),
    'default_memory_limit' => env('IMPORT_DEFAULT_MEMORY_LIMIT', '1G'),
    'default_max_execution_time' => env('IMPORT_DEFAULT_MAX_EXECUTION_TIME', 0),

    'outlet_products' => [
        'chunk_size' => env('IMPORT_OUTLET_PRODUCTS_CHUNK_SIZE', 1000),
        'batch_size' => env('IMPORT_OUTLET_PRODUCTS_BATCH_SIZE', 1000),
        'memory_limit' => env('IMPORT_OUTLET_PRODUCTS_MEMORY_LIMIT', '512M'),
        'max_execution_time' => env('IMPORT_OUTLET_PRODUCTS_MAX_TIME', 300), // 5 minutes
        'enable_logging' => env('IMPORT_OUTLET_PRODUCTS_LOGGING', false),
        'database' => [
            'disable_foreign_key_checks' => env('IMPORT_OUTLET_PRODUCTS_DISABLE_FK_CHECKS', false),
            'disable_query_log' => env('IMPORT_OUTLET_PRODUCTS_DISABLE_QUERY_LOG', true),
            'use_transactions' => env('IMPORT_OUTLET_PRODUCTS_USE_TRANSACTIONS', true),
        ],
    ],

    'report_stocks' => [
        'chunk_size' => env('IMPORT_REPORT_STOCKS_CHUNK_SIZE', 500),
        'batch_size' => env('IMPORT_REPORT_STOCKS_BATCH_SIZE', 500),
        'memory_limit' => env('IMPORT_REPORT_STOCKS_MEMORY_LIMIT', '1G'),
        'max_execution_time' => env('IMPORT_REPORT_STOCKS_MAX_TIME', 0), // No limit
        'enable_logging' => env('IMPORT_REPORT_STOCKS_LOGGING', true),
        'database' => [
            'disable_foreign_key_checks' => env('IMPORT_REPORT_STOCKS_DISABLE_FK_CHECKS', false),
            'disable_query_log' => env('IMPORT_REPORT_STOCKS_DISABLE_QUERY_LOG', true),
            'use_transactions' => env('IMPORT_REPORT_STOCKS_USE_TRANSACTIONS', true),
        ],
    ],

    'report_stock_bulk' => [
        'chunk_size' => env('IMPORT_REPORT_STOCK_BULK_CHUNK_SIZE', 500),
        'batch_size' => env('IMPORT_REPORT_STOCK_BULK_BATCH_SIZE', 500),
        'memory_limit' => env('IMPORT_REPORT_STOCK_BULK_MEMORY_LIMIT', '2G'),
        'max_execution_time' => env('IMPORT_REPORT_STOCK_BULK_MAX_TIME', 0), // No limit
        'enable_logging' => env('IMPORT_REPORT_STOCK_BULK_LOGGING', true),
        'database' => [
            'disable_foreign_key_checks' => env('IMPORT_REPORT_STOCK_BULK_DISABLE_FK_CHECKS', true),
            'disable_query_log' => env('IMPORT_REPORT_STOCK_BULK_DISABLE_QUERY_LOG', true),
            'use_transactions' => env('IMPORT_REPORT_STOCK_BULK_USE_TRANSACTIONS', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Optimization
    |--------------------------------------------------------------------------
    */
    
    'database' => [
        'disable_foreign_key_checks' => env('IMPORT_DISABLE_FK_CHECKS', true),
        'disable_query_log' => env('IMPORT_DISABLE_QUERY_LOG', true),
        'use_transactions' => env('IMPORT_USE_TRANSACTIONS', true),
    ],
];
