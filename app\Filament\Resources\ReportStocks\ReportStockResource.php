<?php

namespace App\Filament\Resources\ReportStocks;

use App\Filament\Resources\ReportStocks\Pages\CreateReportStock;
use App\Filament\Resources\ReportStocks\Pages\EditReportStock;
use App\Filament\Resources\ReportStocks\Pages\ListReportStocks;
use App\Filament\Resources\ReportStocks\Pages\OutletSelection;
use App\Filament\Resources\ReportStocks\Pages\OutletSelectionByDate;
use App\Filament\Resources\ReportStocks\Pages\DateSelection;
use App\Filament\Resources\ReportStocks\Pages\StockDetails;
// use App\Filament\Resources\ReportStocks\Pages\GeneratePurchaseRequest;

use App\Filament\Resources\ReportStocks\Schemas\ReportStockForm;
use App\Filament\Resources\ReportStocks\Tables\ReportStocksTable;
use App\Models\ReportStock;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;


class ReportStockResource extends Resource
{
    protected static ?string $model = ReportStock::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedDocumentText;

    protected static ?string $recordTitleAttribute = 'report_date';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // For admin users, scope to their outlet
        $user = Auth::user();
        if ($user?->hasRole('admin') && $user->outlet_id) {
            $query->whereHas('details', function ($q) use ($user) {
                $q->where('outlet_id', $user->outlet_id);
            });
        }

        // Default ordering: by report date (newest first)
        return $query->orderBy('report_date', 'desc');
    }

    public static function canCreate(): bool
    {
        return true; // Temporarily disable permission check
        // return Auth::user()?->hasPermissionTo('create_report_stocks') ?? false;
    }

    public static function canEdit($record): bool
    {
        return true; // Temporarily disable permission check
        // return Auth::user()?->hasPermissionTo('edit_report_stocks') ?? false;
    }

    public static function canDelete($record): bool
    {
        return true; // Temporarily disable permission check
        // return Auth::user()?->hasPermissionTo('delete_report_stocks') ?? false;
    }

    public static function canViewAny(): bool
    {
        return true; // Temporarily disable permission check
        // return Auth::user()?->hasPermissionTo('view_report_stocks') ?? false;
    }

    public static function form(Schema $schema): Schema
    {
        return ReportStockForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ReportStocksTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListReportStocks::route('/'),
            'outlets' => OutletSelectionByDate::route('/dates/{date}/outlets'),
            'details' => StockDetails::route('/outlets/{outlet}/dates/{date}/details'),
            'create' => CreateReportStock::route('/create'),
            'edit' => EditReportStock::route('/{record}/edit'),
            // 'generate-purchase-request' => GeneratePurchaseRequest::route('/generate-purchase-request'),

            // Legacy routes (keep for backward compatibility)
            'outlet-selection' => OutletSelection::route('/outlets'),
            'dates' => DateSelection::route('/outlets/{outlet}/dates'),
        ];
    }
}
