<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Http\Kernel');

// Create a request to the report stocks page
$request = Illuminate\Http\Request::create('/admin/report-stocks', 'GET');

try {
    echo "Testing page access...\n";
    
    $response = $kernel->handle($request);
    
    echo "Response status: " . $response->getStatusCode() . "\n";
    
    if ($response->getStatusCode() === 200) {
        echo "✅ Page accessible!\n";
        
        $content = $response->getContent();
        if (strpos($content, 'Import Excel') !== false) {
            echo "✅ Import Excel button found in content!\n";
        } else {
            echo "❌ Import Excel button not found in content\n";
        }
        
        if (strpos($content, 'Download Template') !== false) {
            echo "✅ Download Template button found in content!\n";
        } else {
            echo "❌ Download Template button not found in content\n";
        }
        
    } else {
        echo "❌ Page not accessible. Status: " . $response->getStatusCode() . "\n";
        echo "Response content preview:\n";
        echo substr($response->getContent(), 0, 500) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$kernel->terminate($request, $response ?? null);
