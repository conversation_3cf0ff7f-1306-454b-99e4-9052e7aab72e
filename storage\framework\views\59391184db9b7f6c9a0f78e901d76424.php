<div class="space-y-4">
    <div class="grid grid-cols-2 gap-4">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($product->name); ?></h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">Product Information</p>
        </div>
        <div class="text-right">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                <?php echo e($product->barcode); ?>

            </span>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Product Name</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($product->name); ?></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Barcode</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white font-mono"><?php echo e($product->barcode); ?></p>
            </div>
        </div>

        <div class="space-y-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Unit</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($product->unit ?? 'Not specified'); ?></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Pack Quantity</label>
                <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($product->pack_quantity ?? 'Not specified'); ?> units per pack</p>
            </div>
        </div>
    </div>

    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Additional Information</h4>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-600 dark:text-gray-400">Created:</span>
                <span class="text-gray-900 dark:text-white ml-2"><?php echo e($product->created_at->format('M j, Y')); ?></span>
            </div>
            <div>
                <span class="text-gray-600 dark:text-gray-400">Updated:</span>
                <span class="text-gray-900 dark:text-white ml-2"><?php echo e($product->updated_at->format('M j, Y')); ?></span>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\apotek\resources\views\filament\modals\product-details.blade.php ENDPATH**/ ?>