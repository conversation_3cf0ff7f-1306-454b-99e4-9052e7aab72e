<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ImportOptimizationService
{
    protected $startTime;
    protected $startMemory;
    protected $enableLogging;
    protected $logContext;

    public function __construct(bool $enableLogging = null, string $logContext = 'import')
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
        $this->enableLogging = $enableLogging ?? config('import.enable_logging', false);
        $this->logContext = $logContext;
    }

    /**
     * Log progress with memory and timing information
     */
    public function logProgress(string $message, array $context = []): void
    {
        if (!$this->enableLogging) {
            return;
        }

        try {
            $currentTime = microtime(true);
            $currentMemory = memory_get_usage(true);
            $peakMemory = memory_get_peak_usage(true);

            $logData = array_merge($context, [
                'elapsed_time' => round($currentTime - $this->startTime, 2) . 's',
                'current_memory' => $this->formatBytes($currentMemory),
                'peak_memory' => $this->formatBytes($peakMemory),
                'memory_delta' => $this->formatBytes($currentMemory - $this->startMemory),
                'context' => $this->logContext,
            ]);

            Log::info("Import progress: {$message}", $logData);
        } catch (\Exception $e) {
            // Silently fail logging to prevent hanging
        }
    }

    /**
     * Execute optimized import with memory and performance monitoring
     */
    public function executeOptimizedImport(callable $importFunction, string $context = 'import'): array
    {
        $this->logContext = $context;
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        // Disable query log to save memory
        DB::disableQueryLog();

        // Set memory limit if configured
        $memoryLimit = config("import.{$context}.memory_limit");
        if ($memoryLimit) {
            ini_set('memory_limit', $memoryLimit);
        }

        // Set execution time limit if configured
        $timeLimit = config("import.{$context}.max_execution_time", 0);
        set_time_limit($timeLimit);

        $this->logProgress('Starting optimized import', [
            'memory_limit' => ini_get('memory_limit'),
            'time_limit' => $timeLimit,
        ]);

        try {
            $result = $importFunction();

            $endTime = microtime(true);
            $endMemory = memory_get_usage(true);
            $peakMemory = memory_get_peak_usage(true);

            $this->logProgress('Import completed successfully', [
                'total_time' => round($endTime - $startTime, 2) . 's',
                'total_memory_used' => $this->formatBytes($endMemory - $startMemory),
                'peak_memory' => $this->formatBytes($peakMemory),
            ]);

            return [
                'success' => true,
                'result' => $result,
                'stats' => [
                    'execution_time' => $endTime - $startTime,
                    'memory_used' => $endMemory - $startMemory,
                    'peak_memory' => $peakMemory,
                ],
            ];

        } catch (\Exception $e) {
            $this->logProgress('Import failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            throw $e;
        } finally {
            // Force garbage collection
            gc_collect_cycles();
        }
    }

    /**
     * Get current memory usage information
     */
    public function getMemoryUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'current_formatted' => $this->formatBytes(memory_get_usage(true)),
            'peak' => memory_get_peak_usage(true),
            'peak_formatted' => $this->formatBytes(memory_get_peak_usage(true)),
            'limit' => ini_get('memory_limit'),
        ];
    }

    /**
     * Format bytes to human readable format
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Optimize database settings for bulk operations
     */
    public function optimizeDatabaseForBulkOperations(): void
    {
        // Disable foreign key checks if configured
        if (config("import.{$this->logContext}.database.disable_foreign_key_checks", false)) {
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
        }

        // Disable query log if configured
        if (config("import.{$this->logContext}.database.disable_query_log", true)) {
            DB::disableQueryLog();
        }

        $this->logProgress('Database optimized for bulk operations');
    }

    /**
     * Restore database settings after bulk operations
     */
    public function restoreDatabaseSettings(): void
    {
        try {
            // Re-enable foreign key checks
            if (config("import.{$this->logContext}.database.disable_foreign_key_checks", false)) {
                DB::statement('SET FOREIGN_KEY_CHECKS=1');
            }

            $this->logProgress('Database settings restored');
        } catch (\Exception $e) {
            // Silently fail to prevent hanging the import process
            // Log error without using logProgress to avoid potential loops
            if ($this->enableLogging) {
                Log::warning('Failed to restore database settings', [
                    'error' => $e->getMessage(),
                    'context' => $this->logContext
                ]);
            }
        }
    }

    /**
     * Process data in chunks with transaction management
     */
    public function processInChunks(array $data, callable $processor, int $chunkSize = null): array
    {
        $chunkSize = $chunkSize ?? config("import.{$this->logContext}.chunk_size", 1000);
        $chunks = array_chunk($data, $chunkSize);
        $results = [];
        $useTransactions = config("import.{$this->logContext}.database.use_transactions", true);

        $this->logProgress('Processing data in chunks', [
            'total_records' => count($data),
            'chunk_size' => $chunkSize,
            'total_chunks' => count($chunks),
            'use_transactions' => $useTransactions,
        ]);

        foreach ($chunks as $chunkIndex => $chunk) {
            $chunkStartTime = microtime(true);

            if ($useTransactions) {
                DB::beginTransaction();
            }

            try {
                $result = $processor($chunk, $chunkIndex);
                $results[] = $result;

                if ($useTransactions) {
                    DB::commit();
                }

                $chunkEndTime = microtime(true);
                $this->logProgress("Chunk {$chunkIndex} processed", [
                    'chunk_size' => count($chunk),
                    'chunk_time' => round($chunkEndTime - $chunkStartTime, 2) . 's',
                ]);

                // Force garbage collection after each chunk
                gc_collect_cycles();

            } catch (\Exception $e) {
                if ($useTransactions) {
                    DB::rollback();
                }

                $this->logProgress("Chunk {$chunkIndex} failed", [
                    'error' => $e->getMessage(),
                ]);

                throw $e;
            }
        }

        return $results;
    }

    /**
     * Monitor and log query performance
     */
    public function monitorQueries(callable $callback): array
    {
        $startQueries = $this->getQueryCount();
        $startTime = microtime(true);

        $result = $callback();

        $endQueries = $this->getQueryCount();
        $endTime = microtime(true);

        $this->logProgress('Query performance', [
            'queries_executed' => $endQueries - $startQueries,
            'execution_time' => round($endTime - $startTime, 2) . 's',
        ]);

        return [
            'result' => $result,
            'queries_count' => $endQueries - $startQueries,
            'execution_time' => $endTime - $startTime,
        ];
    }

    /**
     * Get current query count (approximation)
     */
    protected function getQueryCount(): int
    {
        // This is a simple approximation - in production you might want to use
        // a more sophisticated query monitoring solution
        return count(DB::getQueryLog());
    }
}
