<?php

namespace App\Imports;

use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Services\ImportOptimizationService;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportStockImport implements ToCollection, WithHeadingRow, WithValidation, WithBatchInserts, WithChunkReading, SkipsEmptyRows
{
    protected $reportDate;
    protected $reportStockId;
    
    // Statistics
    protected $processedRows = 0;
    protected $productsCreated = 0;
    protected $productsUpdated = 0;
    protected $outletProductsCreated = 0;
    protected $outletProductsUpdated = 0;
    protected $reportStockDetailsCreated = 0;
    protected $reportStockDetailsUpdated = 0;
    protected $errors = [];
    protected $skippedOutlets = [];

    // Cache untuk menghindari N+1 queries
    protected $outletCache = [];
    protected $productCache = [];
    protected $outletProductCache = [];
    protected $reportStockDetailCache = [];

    // Optimization service
    protected $optimizationService;

    public function __construct(string $reportDate)
    {
        Log::info('construct');
        $this->reportDate = Carbon::parse($reportDate)->format('Y-m-d');
        $this->optimizationService = new ImportOptimizationService(
            config('import.report_stocks.enable_logging', true),
            'report_stocks'
        );
    }

    public function collection(Collection $rows)
    {
        $this->optimizationService->logProgress('Starting report stock import', [
            'report_date' => $this->reportDate,
            'total_rows' => $rows->count(),
        ]);

        // Optimize database for bulk operations
        $this->optimizationService->optimizeDatabaseForBulkOperations();

        try {
            // Pre-load data untuk menghindari N+1 queries
            $this->preloadData($rows);

            // Get or create report stock
            $this->getOrCreateReportStock();

            // Process data in chunks
            $this->optimizationService->processInChunks(
                $rows->toArray(),
                [$this, 'processChunk'],
                config('import.report_stocks.chunk_size', 500)
            );

            $this->optimizationService->logProgress('Import completed successfully', [
                'processed_rows' => $this->processedRows,
                'products_created' => $this->productsCreated,
                'products_updated' => $this->productsUpdated,
                'outlet_products_created' => $this->outletProductsCreated,
                'outlet_products_updated' => $this->outletProductsUpdated,
                'report_stock_details_created' => $this->reportStockDetailsCreated,
                'report_stock_details_updated' => $this->reportStockDetailsUpdated,
                'errors_count' => count($this->errors),
                'skipped_outlets' => $this->skippedOutlets,
            ]);

        } finally {
            // Restore database settings with timeout protection
            try {
                $this->optimizationService->restoreDatabaseSettings();
            } catch (\Exception $e) {
                // Log error but don't throw to prevent hanging
                Log::warning('Failed to restore database settings in finally block', [
                    'error' => $e->getMessage(),
                    'report_date' => $this->reportDate
                ]);
            }
        }
    }

    /**
     * Pre-load data untuk menghindari N+1 queries
     */
    protected function preloadData(Collection $rows): void
    {
        $this->optimizationService->logProgress('Pre-loading data to avoid N+1 queries');

        // Extract unique values
        $outletCodes = $rows->pluck('outlet')->filter()->unique()->values();
        $barcodes = $rows->pluck('barcode')->filter()->unique()->values();

        // Load outlets
        $outlets = Outlet::whereIn('code', $outletCodes)->get();
        foreach ($outlets as $outlet) {
            $this->outletCache[strtoupper($outlet->code)] = $outlet;
        }

        // Load products
        $products = Product::whereIn('barcode', $barcodes)->get();
        foreach ($products as $product) {
            $this->productCache[$product->barcode] = $product;
        }

        // Load outlet products
        $outletIds = $outlets->pluck('id');
        $productIds = $products->pluck('id');
        
        if ($outletIds->isNotEmpty() && $productIds->isNotEmpty()) {
            $outletProducts = OutletProduct::whereIn('outlet_id', $outletIds)
                ->whereIn('product_id', $productIds)
                ->get();
            
            foreach ($outletProducts as $outletProduct) {
                $key = $outletProduct->outlet_id . '_' . $outletProduct->product_id;
                $this->outletProductCache[$key] = $outletProduct;
            }
        }

        // Load existing report stock details
        if ($this->reportStockId && $outletIds->isNotEmpty() && $productIds->isNotEmpty()) {
            $reportStockDetails = ReportStockDetail::where('report_stock_id', $this->reportStockId)
                ->whereIn('outlet_id', $outletIds)
                ->whereIn('product_id', $productIds)
                ->get();
            
            foreach ($reportStockDetails as $detail) {
                $key = $detail->outlet_id . '_' . $detail->product_id;
                $this->reportStockDetailCache[$key] = $detail;
            }
        }

        $this->optimizationService->logProgress('Data pre-loaded', [
            'outlets_cached' => count($this->outletCache),
            'products_cached' => count($this->productCache),
            'outlet_products_cached' => count($this->outletProductCache),
            'report_stock_details_cached' => count($this->reportStockDetailCache),
        ]);
    }

    /**
     * Get or create report stock for the given date
     */
    protected function getOrCreateReportStock(): void
    {
        $reportStock = ReportStock::firstOrCreate(
            ['report_date' => $this->reportDate],
            ['is_generated' => false]
        );

        $this->reportStockId = $reportStock->id;

        $this->optimizationService->logProgress('Report stock prepared', [
            'report_stock_id' => $this->reportStockId,
            'report_date' => $this->reportDate,
        ]);
    }

    /**
     * Process a chunk of data
     */
    public function processChunk(array $chunk, int $chunkIndex): array
    {
        $productsToUpsert = [];
        $outletProductsToUpsert = [];
        $reportStockDetailsToUpsert = [];
        $chunkErrors = [];

        foreach ($chunk as $rowIndex => $row) {
            $globalRowIndex = ($chunkIndex * config('import.report_stocks.chunk_size', 500)) + $rowIndex + 2; // +2 for header row
            
            try {
                $result = $this->processRow($row, $globalRowIndex);
                
                if ($result['product_data']) {
                    $productsToUpsert[] = $result['product_data'];
                }
                
                if ($result['outlet_product_data']) {
                    $outletProductsToUpsert[] = $result['outlet_product_data'];
                }
                
                if ($result['report_stock_detail_data']) {
                    $reportStockDetailsToUpsert[] = $result['report_stock_detail_data'];
                }

                $this->processedRows++;

            } catch (\Exception $e) {
                $chunkErrors[] = [
                    'row' => $globalRowIndex,
                    'error' => $e->getMessage(),
                    'data' => $row,
                ];
                
                Log::error('Error processing report stock import row', [
                    'row' => $globalRowIndex,
                    'data' => $row,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Batch upsert operations
        DB::transaction(function () use ($productsToUpsert, $outletProductsToUpsert, $reportStockDetailsToUpsert) {
            if (!empty($productsToUpsert)) {
                $this->upsertProducts($productsToUpsert);
            }

            if (!empty($outletProductsToUpsert)) {
                $this->upsertOutletProducts($outletProductsToUpsert);
            }

            if (!empty($reportStockDetailsToUpsert)) {
                $this->upsertReportStockDetails($reportStockDetailsToUpsert);
            }
        });

        // Merge errors
        $this->errors = array_merge($this->errors, $chunkErrors);

        return [
            'processed_rows' => count($chunk),
            'products_to_upsert' => count($productsToUpsert),
            'outlet_products_to_upsert' => count($outletProductsToUpsert),
            'report_stock_details_to_upsert' => count($reportStockDetailsToUpsert),
            'errors' => count($chunkErrors),
        ];
    }

    /**
     * Process a single row
     */
    protected function processRow(array $row, int $rowIndex): array
    {
        // Validate required fields
        $outletCode = trim(strtoupper($row['outlet'] ?? ''));
        $barcode = trim($row['barcode'] ?? '');
        $productName = trim($row['nama_produk'] ?? '');
        $quantity = $this->parseNumeric($row['qty'] ?? 0);

        if (empty($outletCode)) {
            throw new \Exception('Outlet code is required');
        }

        if (empty($barcode)) {
            throw new \Exception('Barcode is required');
        }

        if (empty($productName)) {
            throw new \Exception('Product name is required');
        }

        // if ($quantity < 0) {
        //     throw new \Exception('Quantity must be non-negative');
        // }

        // Check outlet exists
        if (!isset($this->outletCache[$outletCode])) {
            if (!in_array($outletCode, $this->skippedOutlets)) {
                $this->skippedOutlets[] = $outletCode;
            }
            throw new \Exception("Outlet with code '{$outletCode}' not found");
        }

        $outlet = $this->outletCache[$outletCode];

        // Prepare product data
        $unit = trim($row['sat'] ?? 'pcs');
        $packQuantity = max(1, (int) $this->parseNumeric($row['pack'] ?? 1));

        $productData = null;
        $isNewProduct = false;

        if (!isset($this->productCache[$barcode])) {
            // New product
            $productData = [
                'barcode' => $barcode,
                'name' => $productName,
                'unit' => $unit,
                'pack_quantity' => $packQuantity,
                'created_at' => now(),
                'updated_at' => now(),
            ];
            $isNewProduct = true;
        } else {
            // Update existing product
            $existingProduct = $this->productCache[$barcode];
            if ($existingProduct->name !== $productName ||
                $existingProduct->unit !== $unit ||
                $existingProduct->pack_quantity !== $packQuantity) {

                $productData = [
                    'barcode' => $barcode,
                    'name' => $productName,
                    'unit' => $unit,
                    'pack_quantity' => $packQuantity,
                    'updated_at' => now(),
                ];
            }
        }

        // Prepare outlet product data
        $outletPareto = trim($row['prt'] ?? '');

        // Always prepare outlet product data for upsert
        $outletProductData = [
            'outlet_id' => $outlet->id,
            'barcode' => $barcode, // Temporary field for matching after product upsert
            'outlet_pareto' => $outletPareto ?: null,
            'rumus_pareto' => null,
            'min_buffer' => 0,
            'max_buffer' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Prepare report stock detail data
        $reportStockDetailData = [
            'report_stock_id' => $this->reportStockId,
            'outlet_id' => $outlet->id,
            'barcode' => $barcode, // Temporary field for matching after product upsert
            'quantity' => (int) $quantity,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        return [
            'product_data' => $productData,
            'outlet_product_data' => $outletProductData,
            'report_stock_detail_data' => $reportStockDetailData,
        ];
    }

    /**
     * Parse numeric value with comma as decimal separator
     */
    protected function parseNumeric($value): float
    {
        if (is_numeric($value)) {
            return (float) $value;
        }

        // Handle comma as decimal separator
        $value = str_replace(',', '.', (string) $value);
        return is_numeric($value) ? (float) $value : 0;
    }

    /**
     * Upsert products using optimized batch operations
     */
    protected function upsertProducts(array $productsData): void
    {
        if (empty($productsData)) {
            return;
        }

        // Group by operation type
        $toCreate = [];
        $toUpdate = [];

        foreach ($productsData as $productData) {
            $barcode = $productData['barcode'];

            if (!isset($this->productCache[$barcode])) {
                $toCreate[] = $productData;
            } else {
                $toUpdate[] = $productData;
            }
        }

        // Batch create new products
        if (!empty($toCreate)) {
            Product::upsert(
                $toCreate,
                ['barcode'], // unique key
                ['name', 'unit', 'pack_quantity', 'updated_at'] // updateable fields
            );

            // Update cache with new products
            $newProducts = Product::whereIn('barcode', array_column($toCreate, 'barcode'))->get();
            foreach ($newProducts as $product) {
                $this->productCache[$product->barcode] = $product;
            }

            $this->productsCreated += count($toCreate);
        }

        // Batch update existing products
        if (!empty($toUpdate)) {
            Product::upsert(
                $toUpdate,
                ['barcode'], // unique key
                ['name', 'unit', 'pack_quantity', 'updated_at'] // updateable fields
            );

            $this->productsUpdated += count($toUpdate);
        }

        $this->optimizationService->logProgress('Products upserted', [
            'created' => count($toCreate),
            'updated' => count($toUpdate),
        ]);
    }

    /**
     * Upsert outlet products using optimized batch operations
     */
    protected function upsertOutletProducts(array $outletProductsData): void
    {
        if (empty($outletProductsData)) {
            return;
        }

        $processedData = [];

        foreach ($outletProductsData as $outletProductData) {
            $barcode = $outletProductData['barcode'];
            $outletId = $outletProductData['outlet_id'];

            // Get product ID from cache
            if (!isset($this->productCache[$barcode])) {
                continue; // Skip if product not found
            }

            $productId = $this->productCache[$barcode]->id;
            $outletProductData['product_id'] = $productId;
            unset($outletProductData['barcode']); // Remove temporary field

            $processedData[] = $outletProductData;

            // Update cache
            $key = $outletId . '_' . $productId;
            if (!isset($this->outletProductCache[$key])) {
                $this->outletProductsCreated++;
            } else {
                $this->outletProductsUpdated++;
            }
        }

        if (!empty($processedData)) {
            OutletProduct::upsert(
                $processedData,
                ['outlet_id', 'product_id'], // composite unique key
                ['outlet_pareto', 'rumus_pareto', 'min_buffer', 'max_buffer', 'updated_at']
            );
        }

        $this->optimizationService->logProgress('Outlet products upserted', [
            'processed' => count($processedData),
        ]);
    }

    /**
     * Upsert report stock details using optimized batch operations
     */
    protected function upsertReportStockDetails(array $reportStockDetailsData): void
    {
        if (empty($reportStockDetailsData)) {
            return;
        }

        $processedData = [];

        foreach ($reportStockDetailsData as $detailData) {
            $barcode = $detailData['barcode'];
            $outletId = $detailData['outlet_id'];

            // Get product ID from cache
            if (!isset($this->productCache[$barcode])) {
                continue; // Skip if product not found
            }

            $productId = $this->productCache[$barcode]->id;
            $detailData['product_id'] = $productId;
            unset($detailData['barcode']); // Remove temporary field

            $processedData[] = $detailData;

            // Update cache
            $key = $outletId . '_' . $productId;
            if (!isset($this->reportStockDetailCache[$key])) {
                $this->reportStockDetailsCreated++;
            } else {
                $this->reportStockDetailsUpdated++;
            }
        }

        if (!empty($processedData)) {
            ReportStockDetail::upsert(
                $processedData,
                ['report_stock_id', 'outlet_id', 'product_id'], // composite unique key
                ['quantity', 'updated_at']
            );
        }

        $this->optimizationService->logProgress('Report stock details upserted', [
            'processed' => count($processedData),
        ]);
    }

    /**
     * Validation rules
     */
    public function rules(): array
    {
        return [
            'outlet' => 'required|string',
            'barcode' => 'required|string',
            'nama_produk' => 'required|string',
            'qty' => 'required',
            'sat' => 'nullable|string',
            'pack' => 'nullable',
            'prt' => 'nullable|string',
        ];
    }

    /**
     * Batch size for processing
     */
    public function batchSize(): int
    {
        return config('import.report_stocks.batch_size', 500);
    }

    /**
     * Chunk size for reading
     */
    public function chunkSize(): int
    {
        return config('import.report_stocks.chunk_size', 500);
    }

    // Getter methods for statistics
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    public function getProductsCreated(): int
    {
        return $this->productsCreated;
    }

    public function getProductsUpdated(): int
    {
        return $this->productsUpdated;
    }

    public function getOutletProductsCreated(): int
    {
        return $this->outletProductsCreated;
    }

    public function getOutletProductsUpdated(): int
    {
        return $this->outletProductsUpdated;
    }

    public function getReportStockDetailsCreated(): int
    {
        return $this->reportStockDetailsCreated;
    }

    public function getReportStockDetailsUpdated(): int
    {
        return $this->reportStockDetailsUpdated;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getSkippedOutlets(): array
    {
        return $this->skippedOutlets;
    }

    public function getImportSummary(): array
    {
        return [
            'processed_rows' => $this->processedRows,
            'products_created' => $this->productsCreated,
            'products_updated' => $this->productsUpdated,
            'outlet_products_created' => $this->outletProductsCreated,
            'outlet_products_updated' => $this->outletProductsUpdated,
            'report_stock_details_created' => $this->reportStockDetailsCreated,
            'report_stock_details_updated' => $this->reportStockDetailsUpdated,
            'errors_count' => count($this->errors),
            'skipped_outlets' => $this->skippedOutlets,
        ];
    }
}
