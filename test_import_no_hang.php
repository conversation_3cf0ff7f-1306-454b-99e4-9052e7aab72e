<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING IMPORT NO HANG ===\n\n";

// Set timeout for the entire script
set_time_limit(60); // 1 minute max

// Create a simple test file
$testFile = storage_path('app/temp-imports/test_no_hang.xlsx');
$testData = [
    ['OUTLET', 'NAMA PRODUK', 'PRT', 'BARCODE', 'PACK', 'QTY', 'SAT'],
    ['AK001', 'Test Product 1', 'FM', 'TEST001', '1', '10', 'PCS'],
    ['AK002', 'Test Product 2', 'SM', 'TEST002', '1', '20', 'PCS'],
];

// Ensure directory exists
if (!is_dir(dirname($testFile))) {
    mkdir(dirname($testFile), 0755, true);
}

// Create Excel file
$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

foreach ($testData as $rowIndex => $rowData) {
    foreach ($rowData as $colIndex => $cellData) {
        $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
    }
}

$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
$writer->save($testFile);

echo "1. Created test file: {$testFile}\n";

// Test import with timeout
$reportDate = '2025-09-18';
echo "2. Starting import for date: {$reportDate}\n";

$startTime = microtime(true);

try {
    // Create import instance
    $import = new App\Imports\ReportStockImport($reportDate);
    
    echo "   - Import instance created\n";
    
    // Start import with timeout monitoring
    $timeoutSeconds = 30; // 30 seconds timeout
    $importStartTime = microtime(true);
    
    // Use Laravel Excel to import
    Maatwebsite\Excel\Facades\Excel::import($import, $testFile);
    
    $importEndTime = microtime(true);
    $importDuration = $importEndTime - $importStartTime;
    
    echo "   ✅ Import completed successfully in " . round($importDuration, 2) . " seconds\n";
    
    // Get import statistics
    $summary = $import->getImportSummary();
    echo "   - Processed rows: {$summary['processed_rows']}\n";
    echo "   - Products created: {$summary['products_created']}\n";
    echo "   - Report details created: {$summary['report_stock_details_created']}\n";
    
    $errors = $import->getErrors();
    if (!empty($errors)) {
        echo "   - Errors: " . count($errors) . "\n";
        foreach (array_slice($errors, 0, 3) as $error) {
            echo "     • Row {$error['row']}: {$error['error']}\n";
        }
    }
    
} catch (\Exception $e) {
    $importEndTime = microtime(true);
    $importDuration = $importEndTime - $importStartTime;
    
    echo "   ❌ Import failed after " . round($importDuration, 2) . " seconds\n";
    echo "   Error: {$e->getMessage()}\n";
    echo "   File: {$e->getFile()}:{$e->getLine()}\n";
}

$totalTime = microtime(true) - $startTime;
echo "\n3. Total test time: " . round($totalTime, 2) . " seconds\n";

// Clean up
if (file_exists($testFile)) {
    unlink($testFile);
    echo "4. Cleaned up test file\n";
}

// Clean up test data
try {
    App\Models\Product::where('barcode', 'LIKE', 'TEST%')->delete();
    echo "5. Cleaned up test products\n";
} catch (\Exception $e) {
    echo "5. Warning: Could not clean up test products: {$e->getMessage()}\n";
}

echo "\n=== TEST COMPLETED ===\n";

if ($totalTime < 30) {
    echo "✅ Import completed within reasonable time\n";
} else {
    echo "⚠️  Import took longer than expected\n";
}

echo "\nIf this test completes without hanging, the import should work in the browser.\n";
