<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockImport;
use App\Models\Outlet;
use App\Models\Product;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

echo "=== Report Stock Import Performance Test ===\n\n";

// Test configurations
$testSizes = [100, 500, 1000, 2000, 5000];
$maxTestSize = isset($argv[1]) ? (int) $argv[1] : 5000;

// Filter test sizes based on max
$testSizes = array_filter($testSizes, fn($size) => $size <= $maxTestSize);

echo "Testing with data sizes: " . implode(', ', $testSizes) . " rows\n\n";

// Create test outlets
echo "Setting up test outlets...\n";
$outlets = [];
for ($i = 1; $i <= 10; $i++) {
    $code = 'AK' . str_pad($i, 3, '0', STR_PAD_LEFT);
    $outlet = Outlet::firstOrCreate(['code' => $code], ['name' => "Apotek Keluarga {$i}"]);
    $outlets[] = $outlet;
}
echo "Created/verified " . count($outlets) . " outlets\n\n";

// Performance test results
$results = [];

foreach ($testSizes as $testSize) {
    echo "=== Testing with {$testSize} rows ===\n";
    
    $testFile = "performance_test_{$testSize}.xlsx";
    $reportDate = '2025-09-16';
    
    // Create test data
    echo "Generating test data...\n";
    $startGenTime = microtime(true);
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // Set headers
    $headers = ['OUTLET', 'NAMA PRODUK', 'PRT', 'BARCODE', 'PACK', 'QTY', 'SAT'];
    $sheet->fromArray([$headers], null, 'A1');
    
    // Generate test data
    $testData = [];
    $paretos = ['A', 'B', 'C'];
    $units = ['Tablet', 'Capsule', 'Botol', 'Tube', 'Strip'];
    
    for ($i = 1; $i <= $testSize; $i++) {
        $outlet = $outlets[array_rand($outlets)];
        $barcode = 'TEST' . str_pad($i, 10, '0', STR_PAD_LEFT);
        $productName = "Test Product {$i}";
        $pareto = $paretos[array_rand($paretos)];
        $unit = $units[array_rand($units)];
        $pack = rand(1, 10);
        $qty = rand(1, 1000);
        
        $testData[] = [
            $outlet->code,
            $productName,
            $pareto,
            $barcode,
            $pack,
            $qty,
            $unit
        ];
    }
    
    // Write data to Excel
    $row = 2;
    foreach ($testData as $data) {
        $sheet->fromArray([$data], null, 'A' . $row);
        $row++;
    }
    
    $writer = new Xlsx($spreadsheet);
    $writer->save($testFile);
    
    $endGenTime = microtime(true);
    $genTime = $endGenTime - $startGenTime;
    
    echo "Test data generated in " . round($genTime, 2) . " seconds\n";
    
    // Clear existing data for clean test
    DB::table('report_stock_details')->delete();
    DB::table('report_stocks')->delete();
    DB::table('outlet_products')->delete();
    DB::table('products')->delete();
    
    // Test import
    echo "Starting import...\n";
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);
    
    try {
        $import = new ReportStockImport($reportDate);
        Excel::import($import, $testFile);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        $importTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;
        
        // Get statistics
        $summary = $import->getImportSummary();
        $errors = $import->getErrors();
        
        // Store results
        $results[$testSize] = [
            'generation_time' => $genTime,
            'import_time' => $importTime,
            'memory_used' => $memoryUsed,
            'peak_memory' => $peakMemory,
            'processed_rows' => $summary['processed_rows'],
            'products_created' => $summary['products_created'],
            'outlet_products_created' => $summary['outlet_products_created'],
            'report_stock_details_created' => $summary['report_stock_details_created'],
            'errors_count' => count($errors),
            'rows_per_second' => $summary['processed_rows'] / $importTime,
        ];
        
        echo "✅ Import completed successfully!\n";
        echo "Import time: " . round($importTime, 2) . " seconds\n";
        echo "Memory used: " . formatBytes($memoryUsed) . "\n";
        echo "Peak memory: " . formatBytes($peakMemory) . "\n";
        echo "Rows per second: " . round($summary['processed_rows'] / $importTime, 2) . "\n";
        echo "Products created: {$summary['products_created']}\n";
        echo "Errors: " . count($errors) . "\n";
        
    } catch (\Exception $e) {
        echo "❌ Import failed: " . $e->getMessage() . "\n";
        $results[$testSize] = [
            'error' => $e->getMessage(),
            'generation_time' => $genTime,
        ];
    }
    
    // Clean up
    if (file_exists($testFile)) {
        unlink($testFile);
    }
    
    echo "\n";
    
    // Force garbage collection
    gc_collect_cycles();
    
    // Small delay between tests
    sleep(1);
}

// Display summary results
echo "=== Performance Test Summary ===\n\n";
echo sprintf("%-8s %-12s %-12s %-12s %-12s %-15s\n", 
    'Rows', 'Gen Time', 'Import Time', 'Memory', 'Peak Mem', 'Rows/Second');
echo str_repeat('-', 80) . "\n";

foreach ($results as $size => $result) {
    if (isset($result['error'])) {
        echo sprintf("%-8d %-12s %-12s %-12s %-12s %-15s\n", 
            $size, 
            round($result['generation_time'], 2) . 's',
            'FAILED',
            '-',
            '-',
            '-'
        );
    } else {
        echo sprintf("%-8d %-12s %-12s %-12s %-12s %-15s\n", 
            $size,
            round($result['generation_time'], 2) . 's',
            round($result['import_time'], 2) . 's',
            formatBytes($result['memory_used']),
            formatBytes($result['peak_memory']),
            round($result['rows_per_second'], 2)
        );
    }
}

echo "\n=== Recommendations ===\n";

// Calculate average performance
$successfulTests = array_filter($results, fn($r) => !isset($r['error']));
if (!empty($successfulTests)) {
    $avgRowsPerSecond = array_sum(array_column($successfulTests, 'rows_per_second')) / count($successfulTests);
    $maxMemory = max(array_column($successfulTests, 'peak_memory'));
    
    echo "Average processing speed: " . round($avgRowsPerSecond, 2) . " rows/second\n";
    echo "Maximum memory usage: " . formatBytes($maxMemory) . "\n";
    
    // Provide recommendations based on performance
    if ($avgRowsPerSecond > 100) {
        echo "✅ Performance is excellent for production use\n";
    } elseif ($avgRowsPerSecond > 50) {
        echo "✅ Performance is good for production use\n";
    } else {
        echo "⚠️  Performance may need optimization for large datasets\n";
    }
    
    if ($maxMemory > 1024 * 1024 * 1024) { // 1GB
        echo "⚠️  High memory usage detected - consider reducing chunk size\n";
    } else {
        echo "✅ Memory usage is within acceptable limits\n";
    }
}

function formatBytes($bytes)
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

echo "\n=== Performance test completed ===\n";
