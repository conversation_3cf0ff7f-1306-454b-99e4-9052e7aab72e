<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add unique index on barcode for fast lookups during import
            $table->unique('barcode');
            
            // Add index on name for search optimization
            $table->index('name');
        });

        Schema::table('outlets', function (Blueprint $table) {
            // Code is already unique, but add index for fast lookups
            $table->index('code');
        });

        Schema::table('outlet_products', function (Blueprint $table) {
            // Add unique composite index for outlet_id + product_id
            $table->unique(['outlet_id', 'product_id']);
            
            // Add index on outlet_pareto for filtering
            $table->index('outlet_pareto');
        });

        Schema::table('report_stock_details', function (Blueprint $table) {
            // Add unique composite index for report_stock_id + outlet_id + product_id
            $table->unique(['report_stock_id', 'outlet_id', 'product_id'], 'report_stock_details_unique');
            
            // Add index for report_stock_id + outlet_id for faster queries
            $table->index(['report_stock_id', 'outlet_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropUnique(['barcode']);
            $table->dropIndex(['name']);
        });

        Schema::table('outlets', function (Blueprint $table) {
            $table->dropIndex(['code']);
        });

        Schema::table('outlet_products', function (Blueprint $table) {
            $table->dropUnique(['outlet_id', 'product_id']);
            $table->dropIndex(['outlet_pareto']);
        });

        Schema::table('report_stock_details', function (Blueprint $table) {
            $table->dropUnique('report_stock_details_unique');
            $table->dropIndex(['report_stock_id', 'outlet_id']);
        });
    }
};
