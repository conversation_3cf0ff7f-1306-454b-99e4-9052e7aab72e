<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING DRILL-DOWN NAVIGATION ===\n\n";

// Test 1: Check if we have data for testing
echo "1. Checking available data:\n";
$reportStocks = App\Models\ReportStock::with('details.outlet', 'details.product')->get();

if ($reportStocks->isEmpty()) {
    echo "   ❌ No ReportStock data found. Creating test data...\n";
    
    // Create test data if none exists
    $reportDate = '2025-09-16';
    $reportStock = App\Models\ReportStock::firstOrCreate(['report_date' => $reportDate]);
    
    $outlets = App\Models\Outlet::take(3)->get();
    $products = App\Models\Product::take(3)->get();
    
    foreach ($outlets as $outlet) {
        foreach ($products as $product) {
            App\Models\ReportStockDetail::firstOrCreate([
                'report_stock_id' => $reportStock->id,
                'outlet_id' => $outlet->id,
                'product_id' => $product->id,
            ], [
                'quantity' => rand(10, 100)
            ]);
        }
    }
    
    $reportStocks = App\Models\ReportStock::with('details.outlet', 'details.product')->get();
}

foreach ($reportStocks as $reportStock) {
    echo "   ✅ Report Date: {$reportStock->report_date->format('Y-m-d')}\n";
    echo "      - Outlets: {$reportStock->getTotalOutlets()}\n";
    echo "      - Products: {$reportStock->getTotalProducts()}\n";
    echo "      - Total Quantity: {$reportStock->getTotalQuantity()}\n";
}

// Test 2: Check route generation
echo "\n2. Testing route generation:\n";
foreach ($reportStocks as $reportStock) {
    try {
        $outletUrl = route('filament.admin.resources.report-stocks.outlets', [
            'date' => $reportStock->report_date->format('Y-m-d')
        ]);
        echo "   ✅ Outlet selection URL: {$outletUrl}\n";
        
        // Get outlets for this report
        $outlets = App\Models\Outlet::whereHas('reportStockDetails', function ($query) use ($reportStock) {
            $query->where('report_stock_id', $reportStock->id);
        })->take(2)->get();
        
        foreach ($outlets as $outlet) {
            try {
                $detailUrl = route('filament.admin.resources.report-stocks.details', [
                    'outlet' => $outlet->id,
                    'date' => $reportStock->report_date->format('Y-m-d')
                ]);
                echo "   ✅ Product details URL for {$outlet->name}: {$detailUrl}\n";
            } catch (Exception $e) {
                echo "   ❌ Error generating detail URL for {$outlet->name}: {$e->getMessage()}\n";
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Error generating outlet URL: {$e->getMessage()}\n";
    }
}

// Test 3: Test navigation flow
echo "\n3. Testing navigation flow:\n";
$firstReport = $reportStocks->first();
if ($firstReport) {
    echo "   📊 Starting from Report Date: {$firstReport->report_date->format('Y-m-d')}\n";
    
    // Get outlets for this report
    $outlets = App\Models\Outlet::whereHas('reportStockDetails', function ($query) use ($firstReport) {
        $query->where('report_stock_id', $firstReport->id);
    })->get();
    
    echo "   🏪 Available outlets ({$outlets->count()}):\n";
    foreach ($outlets as $outlet) {
        $productCount = App\Models\ReportStockDetail::where('report_stock_id', $firstReport->id)
            ->where('outlet_id', $outlet->id)
            ->count();
        
        $totalQuantity = App\Models\ReportStockDetail::where('report_stock_id', $firstReport->id)
            ->where('outlet_id', $outlet->id)
            ->sum('quantity');
            
        echo "      - {$outlet->code} ({$outlet->name}): {$productCount} products, {$totalQuantity} total qty\n";
        
        // Get products for this outlet
        $details = App\Models\ReportStockDetail::where('report_stock_id', $firstReport->id)
            ->where('outlet_id', $outlet->id)
            ->with('product')
            ->take(3)
            ->get();
            
        echo "        📦 Sample products:\n";
        foreach ($details as $detail) {
            echo "           • {$detail->product->name} (Barcode: {$detail->product->barcode}): {$detail->quantity} units\n";
        }
    }
}

// Test 4: Check page classes exist
echo "\n4. Testing page classes:\n";
$pageClasses = [
    'App\\Filament\\Resources\\ReportStocks\\Pages\\ListReportStocks',
    'App\\Filament\\Resources\\ReportStocks\\Pages\\OutletSelectionByDate',
    'App\\Filament\\Resources\\ReportStocks\\Pages\\StockDetails',
];

foreach ($pageClasses as $class) {
    if (class_exists($class)) {
        echo "   ✅ {$class}\n";
    } else {
        echo "   ❌ {$class} - NOT FOUND\n";
    }
}

echo "\n=== NAVIGATION FLOW SUMMARY ===\n";
echo "1. 📊 Report Stocks List (ListReportStocks)\n";
echo "   └─ Click on report date row\n";
echo "   \n";
echo "2. 🏪 Outlet Selection (OutletSelectionByDate)\n";
echo "   └─ Shows outlets that have data for selected date\n";
echo "   └─ Click on outlet row\n";
echo "   \n";
echo "3. 📦 Product Details (StockDetails)\n";
echo "   └─ Shows all products for selected outlet and date\n";
echo "   └─ Displays quantities, pareto, etc.\n";

echo "\n=== USAGE INSTRUCTIONS ===\n";
echo "1. Go to Report Stocks page in Filament admin\n";
echo "2. Click on any report date row to see outlets\n";
echo "3. Click on any outlet row to see product details\n";
echo "4. Use breadcrumbs or back buttons to navigate back\n";

echo "\n✅ Drill-down navigation is ready!\n";
