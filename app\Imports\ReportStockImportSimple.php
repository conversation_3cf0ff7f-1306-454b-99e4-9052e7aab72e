<?php

namespace App\Imports;

use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;

class ReportStockImportSimple implements ToCollection, WithHeadingRow, WithValidation, SkipsEmptyRows
{
    protected $reportDate;
    protected $reportStockId;
    
    // Statistics
    protected $processedRows = 0;
    protected $productsCreated = 0;
    protected $productsUpdated = 0;
    protected $outletProductsCreated = 0;
    protected $outletProductsUpdated = 0;
    protected $reportStockDetailsCreated = 0;
    protected $reportStockDetailsUpdated = 0;
    protected $errors = [];
    protected $skippedOutlets = [];

    public function __construct(string $reportDate)
    {
        $this->reportDate = Carbon::parse($reportDate)->format('Y-m-d');
    }

    public function collection(Collection $rows)
    {
        Log::info('Starting simple import');

        try {
            // Initialize report stock
            $this->initializeReportStock();

            // Process each row
            foreach ($rows as $index => $row) {
                try {
                    $this->processRowSimple($row->toArray(), $index + 2); // +2 for header and 1-based index
                    $this->processedRows++;
                } catch (\Exception $e) {
                    $this->errors[] = [
                        'row' => $index + 2,
                        'error' => $e->getMessage(),
                        'data' => $row->toArray(),
                    ];
                    Log::error('Error processing row', [
                        'row' => $index + 2,
                        'error' => $e->getMessage(),
                        'data' => $row->toArray(),
                    ]);
                }
            }

            Log::info('Simple import completed', [
                'processed_rows' => $this->processedRows,
                'errors_count' => count($this->errors),
            ]);

        } catch (\Exception $e) {
            Log::error('Simple import failed', [
                'error' => $e->getMessage(),
                'report_date' => $this->reportDate
            ]);
            throw $e;
        }
    }

    protected function initializeReportStock(): void
    {
        $reportStock = ReportStock::firstOrCreate(
            ['report_date' => $this->reportDate],
            ['report_date' => $this->reportDate]
        );
        
        $this->reportStockId = $reportStock->id;
        Log::info('Report stock initialized', ['id' => $this->reportStockId]);
    }

    protected function processRowSimple(array $row, int $rowIndex): void
    {
        // Validate required fields
        $outletCode = trim(strtoupper($row['outlet'] ?? ''));
        $productName = trim($row['nama_produk'] ?? '');
        $barcode = trim($row['barcode'] ?? '');
        $quantity = max(0, (int) ($row['qty'] ?? 0));

        if (empty($outletCode) || empty($barcode)) {
            throw new \Exception('Missing required fields: outlet or barcode');
        }

        // Get outlet
        $outlet = Outlet::where('code', $outletCode)->first();
        if (!$outlet) {
            if (!in_array($outletCode, $this->skippedOutlets)) {
                $this->skippedOutlets[] = $outletCode;
            }
            throw new \Exception("Outlet not found: {$outletCode}");
        }

        // Process product
        $product = Product::where('barcode', $barcode)->first();
        if (!$product) {
            $product = Product::create([
                'name' => $productName,
                'barcode' => $barcode,
                'pack_quantity' => max(1, (int) ($row['pack'] ?? 1)),
                'unit' => trim($row['sat'] ?? 'PCS'),
            ]);
            $this->productsCreated++;
        } else {
            // Update product info
            $product->update([
                'name' => $productName,
                'pack_quantity' => max(1, (int) ($row['pack'] ?? 1)),
                'unit' => trim($row['sat'] ?? 'PCS'),
            ]);
            $this->productsUpdated++;
        }

        // Process outlet product
        $outletProduct = OutletProduct::where('outlet_id', $outlet->id)
            ->where('product_id', $product->id)
            ->first();

        $pareto = trim(strtoupper($row['prt'] ?? ''));
        if (!$outletProduct) {
            OutletProduct::create([
                'outlet_id' => $outlet->id,
                'product_id' => $product->id,
                'outlet_pareto' => $pareto,
            ]);
            $this->outletProductsCreated++;
        } else {
            $outletProduct->update(['outlet_pareto' => $pareto]);
            $this->outletProductsUpdated++;
        }

        // Process report stock detail
        $reportStockDetail = ReportStockDetail::where('report_stock_id', $this->reportStockId)
            ->where('outlet_id', $outlet->id)
            ->where('product_id', $product->id)
            ->first();

        if (!$reportStockDetail) {
            ReportStockDetail::create([
                'report_stock_id' => $this->reportStockId,
                'outlet_id' => $outlet->id,
                'product_id' => $product->id,
                'quantity' => $quantity,
            ]);
            $this->reportStockDetailsCreated++;
        } else {
            $reportStockDetail->update(['quantity' => $quantity]);
            $this->reportStockDetailsUpdated++;
        }
    }

    public function rules(): array
    {
        return [
            'outlet' => 'required|string',
            'nama_produk' => 'required|string',
            'barcode' => 'required|string',
            'qty' => 'required|numeric',
            'pack' => 'nullable|numeric',
            'prt' => 'nullable|string',
            'sat' => 'nullable|string',
        ];
    }

    public function getImportSummary(): array
    {
        return [
            'processed_rows' => $this->processedRows,
            'products_created' => $this->productsCreated,
            'products_updated' => $this->productsUpdated,
            'outlet_products_created' => $this->outletProductsCreated,
            'outlet_products_updated' => $this->outletProductsUpdated,
            'report_stock_details_created' => $this->reportStockDetailsCreated,
            'report_stock_details_updated' => $this->reportStockDetailsUpdated,
            'skipped_outlets' => $this->skippedOutlets,
        ];
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
