<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Imports\ReportStockImport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ReportStockImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test outlets
        Outlet::create(['name' => 'Outlet 1', 'code' => 'OUT001']);
        Outlet::create(['name' => 'Outlet 2', 'code' => 'OUT002']);
    }

    public function test_can_import_new_products_and_report_stock_details()
    {
        $reportDate = '2025-09-16';
        
        // Create test data
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'New Product',
                'prt' => 'A',
                'barcode' => '1234567890001',
                'pack' => '1',
                'qty' => 100,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockImport($reportDate);
        $import->collection($rows);

        // Assert report stock was created
        $this->assertDatabaseHas('report_stocks', [
            'report_date' => $reportDate,
        ]);

        // Assert product was created
        $this->assertDatabaseHas('products', [
            'barcode' => '1234567890001',
            'name' => 'New Product',
            'unit' => 'PCS',
            'pack_quantity' => 1,
        ]);

        // Assert outlet product was created
        $outlet = Outlet::where('code', 'OUT001')->first();
        $product = Product::where('barcode', '1234567890001')->first();
        
        $this->assertDatabaseHas('outlet_products', [
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'outlet_pareto' => 'A',
        ]);

        // Assert report stock detail was created
        $reportStock = ReportStock::where('report_date', $reportDate)->first();
        
        $this->assertDatabaseHas('report_stock_details', [
            'report_stock_id' => $reportStock->id,
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'quantity' => 100,
        ]);
    }

    public function test_can_update_existing_products_and_report_stock_details()
    {
        $reportDate = '2025-09-16';
        
        // Create existing data
        $outlet = Outlet::where('code', 'OUT001')->first();
        $product = Product::create([
            'name' => 'Old Product Name',
            'barcode' => '1234567890002',
            'unit' => 'BOX',
            'pack_quantity' => 5,
        ]);
        
        $outletProduct = OutletProduct::create([
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'outlet_pareto' => 'B',
        ]);

        $reportStock = ReportStock::create(['report_date' => $reportDate]);
        $reportStockDetail = ReportStockDetail::create([
            'report_stock_id' => $reportStock->id,
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'quantity' => 50,
        ]);

        // Import with updated data
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Updated Product Name',
                'prt' => 'A',
                'barcode' => '1234567890002',
                'pack' => '10',
                'qty' => 150,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockImport($reportDate);
        $import->collection($rows);

        // Assert product was updated
        $product->refresh();
        $this->assertEquals('Updated Product Name', $product->name);
        $this->assertEquals('PCS', $product->unit);
        $this->assertEquals(10, $product->pack_quantity);

        // Assert outlet product was updated
        $outletProduct->refresh();
        $this->assertEquals('A', $outletProduct->outlet_pareto);

        // Assert report stock detail was updated
        $reportStockDetail->refresh();
        $this->assertEquals(150, $reportStockDetail->quantity);
    }

    public function test_handles_missing_outlet_gracefully()
    {
        $reportDate = '2025-09-16';
        
        $rows = collect([
            [
                'outlet' => 'NONEXISTENT',
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '1234567890003',
                'pack' => '1',
                'qty' => 100,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockImport($reportDate);
        $import->collection($rows);

        // Check that error was recorded
        $errors = $import->getErrors();
        $this->assertCount(1, $errors);
        $this->assertStringContains('not found', $errors[0]['error']);

        // Check that outlet was marked as skipped
        $skippedOutlets = $import->getSkippedOutlets();
        $this->assertContains('NONEXISTENT', $skippedOutlets);

        // Assert no product was created
        $this->assertDatabaseMissing('products', [
            'barcode' => '1234567890003',
        ]);
    }

    public function test_handles_missing_required_data()
    {
        $reportDate = '2025-09-16';
        
        $rows = collect([
            [
                'outlet' => '', // Missing outlet
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '1234567890004',
                'pack' => '1',
                'qty' => 100,
                'sat' => 'PCS',
            ],
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '', // Missing barcode
                'pack' => '1',
                'qty' => 100,
                'sat' => 'PCS',
            ],
            [
                'outlet' => 'OUT001',
                'nama_produk' => '', // Missing product name
                'prt' => 'A',
                'barcode' => '1234567890005',
                'pack' => '1',
                'qty' => 100,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockImport($reportDate);
        $import->collection($rows);

        // Check statistics
        $this->assertEquals(3, $import->getProcessedRows());
        $this->assertEquals(0, $import->getProductsCreated());
        $this->assertEquals(3, count($import->getErrors()));
    }

    public function test_parses_numeric_values_correctly()
    {
        $reportDate = '2025-09-16';
        
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '1234567890006',
                'pack' => '2,50', // Comma as decimal separator
                'qty' => '100,75', // Comma as decimal separator
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockImport($reportDate);
        $import->collection($rows);

        // Assert product was created with correct pack quantity
        $this->assertDatabaseHas('products', [
            'barcode' => '1234567890006',
            'pack_quantity' => 2, // Should be converted to integer
        ]);

        // Assert report stock detail was created with correct quantity
        $outlet = Outlet::where('code', 'OUT001')->first();
        $product = Product::where('barcode', '1234567890006')->first();
        $reportStock = ReportStock::where('report_date', $reportDate)->first();
        
        $this->assertDatabaseHas('report_stock_details', [
            'report_stock_id' => $reportStock->id,
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'quantity' => 100, // Should be converted to integer
        ]);
    }

    public function test_import_summary_provides_correct_statistics()
    {
        $reportDate = '2025-09-16';
        
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Product 1',
                'prt' => 'A',
                'barcode' => '1234567890007',
                'pack' => '1',
                'qty' => 100,
                'sat' => 'PCS',
            ],
            [
                'outlet' => 'OUT002',
                'nama_produk' => 'Product 2',
                'prt' => 'B',
                'barcode' => '1234567890008',
                'pack' => '1',
                'qty' => 200,
                'sat' => 'BOX',
            ],
        ]);

        $import = new ReportStockImport($reportDate);
        $import->collection($rows);

        $summary = $import->getImportSummary();

        $this->assertEquals(2, $summary['processed_rows']);
        $this->assertEquals(2, $summary['products_created']);
        $this->assertEquals(2, $summary['outlet_products_created']);
        $this->assertEquals(2, $summary['report_stock_details_created']);
        $this->assertEquals(0, $summary['errors_count']);
        $this->assertEmpty($summary['skipped_outlets']);
    }
}
