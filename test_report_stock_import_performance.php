<?php

require_once 'vendor/autoload.php';

use App\Imports\ReportStockImport;
use App\Models\Outlet;
use App\Models\Product;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * Performance test script for Report Stock Import
 * 
 * This script generates test data and measures import performance
 * with different data sizes to ensure the system can handle
 * hundreds of thousands of rows efficiently.
 */

echo "=== Report Stock Import Performance Test ===\n\n";

// Test configurations
$testSizes = [
    'small' => 1000,      // 1K rows
    'medium' => 5000,     // 5K rows
    'large' => 10000,     // 10K rows
    'xlarge' => 25000,    // 25K rows
    'xxlarge' => 50000,   // 50K rows
];

$reportDate = '2025-09-16';

// Ensure we have test outlets
echo "1. Preparing test outlets...\n";
$testOutlets = [];
for ($i = 1; $i <= 10; $i++) {
    $code = 'TEST' . str_pad($i, 3, '0', STR_PAD_LEFT);
    $outlet = Outlet::firstOrCreate(
        ['code' => $code],
        ['name' => "Test Outlet {$i}"]
    );
    $testOutlets[] = $outlet;
}
echo "   ✅ " . count($testOutlets) . " test outlets ready\n\n";

// Function to generate test Excel file
function generateTestFile($size, $outlets) {
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // Headers
    $headers = ['OUTLET', 'NAMA PRODUK', 'PRT', 'BARCODE', 'PACK', 'QTY', 'SAT'];
    $sheet->fromArray([$headers], null, 'A1');
    
    // Generate test data
    $paretoOptions = ['A', 'B', 'C'];
    $unitOptions = ['Tablet', 'Capsule', 'Botol', 'Strip', 'Tube'];
    
    for ($i = 1; $i <= $size; $i++) {
        $outlet = $outlets[array_rand($outlets)];
        $barcode = 'TEST' . str_pad($i, 8, '0', STR_PAD_LEFT);
        $productName = "Test Product {$i}";
        $pareto = $paretoOptions[array_rand($paretoOptions)];
        $pack = rand(1, 20);
        $qty = rand(10, 1000);
        $unit = $unitOptions[array_rand($unitOptions)];
        
        $row = [
            $outlet->code,
            $productName,
            $pareto,
            $barcode,
            $pack,
            $qty,
            $unit
        ];
        
        $sheet->fromArray([$row], null, 'A' . ($i + 1));
    }
    
    // Save to temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_import_') . '.xlsx';
    $writer = new Xlsx($spreadsheet);
    $writer->save($tempFile);
    
    return $tempFile;
}

// Function to format bytes
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// Function to run performance test
function runPerformanceTest($size, $outlets, $reportDate) {
    echo "Generating test file with {$size} rows...\n";
    $startGeneration = microtime(true);
    $testFile = generateTestFile($size, $outlets);
    $generationTime = microtime(true) - $startGeneration;
    echo "   File generated in " . round($generationTime, 2) . " seconds\n";
    
    // Measure memory before import
    $memoryBefore = memory_get_usage(true);
    $peakMemoryBefore = memory_get_peak_usage(true);
    
    echo "   Starting import...\n";
    $startImport = microtime(true);
    
    try {
        $import = new ReportStockImport($reportDate);
        Excel::import($import, $testFile);
        
        $endImport = microtime(true);
        $importTime = $endImport - $startImport;
        
        // Measure memory after import
        $memoryAfter = memory_get_usage(true);
        $peakMemoryAfter = memory_get_peak_usage(true);
        
        // Get import statistics
        $summary = $import->getImportSummary();
        $errors = $import->getErrors();
        
        // Calculate performance metrics
        $rowsPerSecond = $size / $importTime;
        $memoryUsed = $memoryAfter - $memoryBefore;
        $peakMemoryUsed = $peakMemoryAfter - $peakMemoryBefore;
        
        echo "   ✅ Import completed successfully!\n";
        echo "   📊 Performance Metrics:\n";
        echo "      - Import time: " . round($importTime, 2) . " seconds\n";
        echo "      - Rows per second: " . round($rowsPerSecond, 2) . "\n";
        echo "      - Memory used: " . formatBytes($memoryUsed) . "\n";
        echo "      - Peak memory: " . formatBytes($peakMemoryUsed) . "\n";
        echo "   📈 Import Statistics:\n";
        echo "      - Processed rows: {$summary['processed_rows']}\n";
        echo "      - Products created: {$summary['products_created']}\n";
        echo "      - Products updated: {$summary['products_updated']}\n";
        echo "      - Outlet products created: {$summary['outlet_products_created']}\n";
        echo "      - Outlet products updated: {$summary['outlet_products_updated']}\n";
        echo "      - Report details created: {$summary['report_stock_details_created']}\n";
        echo "      - Report details updated: {$summary['report_stock_details_updated']}\n";
        echo "      - Errors: " . count($errors) . "\n";
        
        if (!empty($errors)) {
            echo "   ⚠️  First 3 errors:\n";
            foreach (array_slice($errors, 0, 3) as $error) {
                echo "      - Row {$error['row']}: {$error['error']}\n";
            }
        }
        
        return [
            'success' => true,
            'size' => $size,
            'import_time' => $importTime,
            'rows_per_second' => $rowsPerSecond,
            'memory_used' => $memoryUsed,
            'peak_memory' => $peakMemoryUsed,
            'summary' => $summary,
            'errors_count' => count($errors)
        ];
        
    } catch (Exception $e) {
        echo "   ❌ Import failed: " . $e->getMessage() . "\n";
        return [
            'success' => false,
            'size' => $size,
            'error' => $e->getMessage()
        ];
    } finally {
        // Clean up test file
        if (file_exists($testFile)) {
            unlink($testFile);
        }
    }
}

// Run performance tests
$results = [];
foreach ($testSizes as $testName => $size) {
    echo "2. Running {$testName} test ({$size} rows)...\n";
    $result = runPerformanceTest($size, $testOutlets, $reportDate);
    $results[$testName] = $result;
    echo "\n";
    
    // Add delay between tests to allow system recovery
    if ($size >= 10000) {
        echo "   Waiting 5 seconds for system recovery...\n";
        sleep(5);
    }
}

// Summary report
echo "=== PERFORMANCE TEST SUMMARY ===\n\n";
echo sprintf("%-10s %-8s %-12s %-15s %-12s %-12s %-8s\n", 
    'Test', 'Rows', 'Time (s)', 'Rows/sec', 'Memory', 'Peak Mem', 'Errors');
echo str_repeat('-', 80) . "\n";

foreach ($results as $testName => $result) {
    if ($result['success']) {
        echo sprintf("%-10s %-8d %-12.2f %-15.2f %-12s %-12s %-8d\n",
            $testName,
            $result['size'],
            $result['import_time'],
            $result['rows_per_second'],
            formatBytes($result['memory_used']),
            formatBytes($result['peak_memory']),
            $result['errors_count']
        );
    } else {
        echo sprintf("%-10s %-8d %-12s %-15s %-12s %-12s %-8s\n",
            $testName,
            $result['size'],
            'FAILED',
            '-',
            '-',
            '-',
            '-'
        );
    }
}

echo "\n=== RECOMMENDATIONS ===\n";
echo "Based on the test results:\n";

$successfulTests = array_filter($results, fn($r) => $r['success']);
if (!empty($successfulTests)) {
    $avgRowsPerSecond = array_sum(array_column($successfulTests, 'rows_per_second')) / count($successfulTests);
    echo "- Average processing speed: " . round($avgRowsPerSecond, 2) . " rows/second\n";
    
    $maxMemory = max(array_column($successfulTests, 'peak_memory'));
    echo "- Peak memory usage: " . formatBytes($maxMemory) . "\n";
    
    echo "- For 100K rows, estimated time: " . round(100000 / $avgRowsPerSecond / 60, 1) . " minutes\n";
    echo "- For 500K rows, estimated time: " . round(500000 / $avgRowsPerSecond / 60, 1) . " minutes\n";
    
    if ($maxMemory > 1024 * 1024 * 1024) { // > 1GB
        echo "- Consider increasing server memory for large imports\n";
    }
    
    echo "- Recommended chunk size: 1000-2000 rows for optimal performance\n";
} else {
    echo "- All tests failed. Please check system configuration.\n";
}

echo "\n=== TEST COMPLETED ===\n";
