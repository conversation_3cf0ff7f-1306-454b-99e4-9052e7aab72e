<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING SIMPLE IMPORT ===\n\n";

// Set timeout
set_time_limit(30);

// Create test file
$testFile = storage_path('app/temp-imports/test_simple.xlsx');
$testData = [
    ['OUTLET', 'NAMA PRODUK', 'PRT', 'BARCODE', 'PACK', 'QTY', 'SAT'],
    ['AK001', 'Test Product Simple 1', 'FM', 'SIMPLE001', '1', '10', 'PCS'],
    ['AK002', 'Test Product Simple 2', 'SM', 'SIMPLE002', '1', '20', 'PCS'],
];

// Ensure directory exists
if (!is_dir(dirname($testFile))) {
    mkdir(dirname($testFile), 0755, true);
}

// Create Excel file
$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

foreach ($testData as $rowIndex => $rowData) {
    foreach ($rowData as $colIndex => $cellData) {
        $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
    }
}

$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
$writer->save($testFile);

echo "1. Created test file\n";

// Test simple import
$reportDate = '2025-09-18';
echo "2. Testing simple import for date: {$reportDate}\n";

$startTime = microtime(true);

try {
    $import = new App\Imports\ReportStockImportSimple($reportDate);
    echo "   - Import instance created\n";
    
    Maatwebsite\Excel\Facades\Excel::import($import, $testFile);
    
    $endTime = microtime(true);
    $duration = $endTime - $startTime;
    
    echo "   ✅ Import completed in " . round($duration, 2) . " seconds\n";
    
    $summary = $import->getImportSummary();
    echo "   - Processed rows: {$summary['processed_rows']}\n";
    echo "   - Products created: {$summary['products_created']}\n";
    echo "   - Report details created: {$summary['report_stock_details_created']}\n";
    
    $errors = $import->getErrors();
    if (!empty($errors)) {
        echo "   - Errors: " . count($errors) . "\n";
    }
    
} catch (\Exception $e) {
    $endTime = microtime(true);
    $duration = $endTime - $startTime;
    
    echo "   ❌ Import failed after " . round($duration, 2) . " seconds\n";
    echo "   Error: {$e->getMessage()}\n";
}

// Clean up
if (file_exists($testFile)) {
    unlink($testFile);
}

App\Models\Product::where('barcode', 'LIKE', 'SIMPLE%')->delete();

echo "\n=== SIMPLE IMPORT TEST COMPLETED ===\n";
