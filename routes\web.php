<?php

use Illuminate\Support\Facades\Route;
use App\Services\ReportStockTemplateService;

Route::get('/', function () {
    return redirect('/admin');
});

Route::get('/report-stock/download-template', function () {
    try {
        $templateService = new ReportStockTemplateService();
        $templatePath = $templateService->generateTemplate();
        $fileName = 'report_stock_template_' . now()->format('Y-m-d_H-i-s') . '.xlsx';

        return response()->download($templatePath, $fileName)->deleteFileAfterSend(true);
    } catch (\Exception $e) {
        abort(500, 'Failed to generate template: ' . $e->getMessage());
    }
})->name('report-stock.download-template');
