# Report Stock Import - User Guide

## Overview

Fitur import Excel untuk Report Stock memungkinkan Anda mengimpor data stok dari file Excel dengan format yang telah ditentukan. Fitur ini dioptimalkan untuk menangani ratusan ribu baris data dengan performa tinggi.

## Cara Menggunakan

### 1. <PERSON><PERSON><PERSON> Report Stock
- Login ke sistem Filament
- Navigasi ke menu "Report Stocks"
- Anda akan melihat halaman daftar report stock

### 2. Download Template Excel
- Klik tombol "Download Template" di bagian atas halaman
- Template Excel akan diunduh dengan format yang benar
- Template berisi:
  - Sheet "Report Stock Template" dengan contoh data
  - Sheet "Instructions" dengan panduan lengkap
  - Sheet "Outlets" dengan daftar outlet yang tersedia

### 3. Persiapkan Data Excel
Pastikan file Excel Anda memiliki kolom-kolom berikut:

| Kolom | Deskripsi | Wajib | Contoh |
|-------|-----------|-------|--------|
| OUTLET | Kode outlet yang sudah ada di sistem | Ya | AK001 |
| NAMA PRODUK | Nama produk | Ya | Paracetamol 500mg |
| PRT | Outlet pareto (A/B/C) | Tidak | A |
| BARCODE | Barcode produk (unique identifier) | Ya | PAR500001 |
| PACK | Pack quantity | Tidak | 10 |
| QTY | Quantity stock | Ya | 100 |
| SAT | Satuan unit | Tidak | Tablet |

### 4. Import Data
- Klik tombol "Import Excel" di bagian atas halaman
- Modal import akan terbuka
- Pilih tanggal report yang diinginkan
- Upload file Excel Anda (maksimal 10MB)
- Klik tombol "Import" untuk memulai proses

### 5. Monitor Progress
- Sistem akan menampilkan notifikasi "Import Started"
- Proses import berjalan di background
- Setelah selesai, Anda akan menerima notifikasi dengan statistik import

## Logic Import

### 1. Cek Report Date
- Sistem akan mencari atau membuat record ReportStock berdasarkan tanggal yang dipilih
- Jika sudah ada, akan menggunakan ID yang ada
- Jika belum ada, akan membuat record baru

### 2. Validasi Outlet
- Sistem akan mengecek apakah kode outlet ada di database
- Jika outlet tidak ditemukan, baris akan dilewati
- Outlet yang dilewati akan dilaporkan di akhir import

### 3. Manajemen Product
- Sistem akan mengecek product berdasarkan barcode
- **Jika product ada**: Update nama, pack quantity, dan unit
- **Jika product tidak ada**: Buat product baru dengan data dari Excel

### 4. Manajemen Outlet Product
- Sistem akan mengecek relasi outlet-product berdasarkan outlet_id dan product_id
- **Jika relasi ada**: Update outlet pareto
- **Jika relasi tidak ada**: Buat relasi baru dengan outlet pareto

### 5. Manajemen Report Stock Detail
- Sistem akan mengecek detail berdasarkan report_stock_id, outlet_id, dan product_id
- **Jika detail ada**: Update quantity
- **Jika detail tidak ada**: Buat detail baru

## Optimasi Performance

### 1. Batch Processing
- Data diproses dalam chunk (default: 500 baris per chunk)
- Menggunakan upsert operations untuk efisiensi database
- Transaksi per chunk untuk mengurangi lock time

### 2. Caching
- Pre-load semua outlet, product, dan relasi yang diperlukan
- Cache data untuk menghindari N+1 queries
- Memory management yang optimal

### 3. Database Optimization
- Index yang optimal pada kolom-kolom kunci
- Foreign key checks dapat dinonaktifkan untuk import besar
- Query log dinonaktifkan untuk menghemat memory

### 4. Konfigurasi untuk Data Besar
Untuk import ratusan ribu baris, gunakan konfigurasi berikut di `.env`:

```env
# Report Stock Import Configuration
IMPORT_REPORT_STOCKS_CHUNK_SIZE=1000
IMPORT_REPORT_STOCKS_BATCH_SIZE=1000
IMPORT_REPORT_STOCKS_MEMORY_LIMIT=2G
IMPORT_REPORT_STOCKS_MAX_TIME=0
IMPORT_REPORT_STOCKS_LOGGING=true
IMPORT_REPORT_STOCKS_DISABLE_FK_CHECKS=true
IMPORT_REPORT_STOCKS_DISABLE_QUERY_LOG=true
IMPORT_REPORT_STOCKS_USE_TRANSACTIONS=true
```

## Error Handling

### 1. Validasi Data
- Outlet code wajib diisi dan harus ada di sistem
- Barcode wajib diisi
- Nama produk wajib diisi
- Quantity harus berupa angka non-negatif

### 2. Error Reporting
- Error akan ditampilkan dengan nomor baris yang bermasalah
- Maksimal 5 error pertama ditampilkan di notifikasi
- Error lengkap dapat dilihat di log sistem

### 3. Rollback
- Jika terjadi error dalam satu chunk, chunk tersebut akan di-rollback
- Chunk lain yang sudah berhasil tidak akan terpengaruh

## Tips Penggunaan

### 1. Persiapan Data
- Pastikan semua outlet sudah ada di sistem sebelum import
- Gunakan barcode yang konsisten dan unik
- Periksa format data sebelum import

### 2. Performance
- Untuk file besar (>100k baris), lakukan import di luar jam sibuk
- Monitor penggunaan memory server
- Gunakan format .xlsx untuk performa terbaik

### 3. Troubleshooting
- Jika import gagal, periksa log di `storage/logs/laravel.log`
- Pastikan file Excel tidak corrupt
- Periksa koneksi database stabil

## Statistik Import

Setelah import selesai, Anda akan melihat statistik berikut:
- **Processed rows**: Jumlah baris yang diproses
- **Products created**: Jumlah produk baru yang dibuat
- **Products updated**: Jumlah produk yang diupdate
- **Outlet products created**: Jumlah relasi outlet-produk baru
- **Outlet products updated**: Jumlah relasi outlet-produk yang diupdate
- **Report stock details created**: Jumlah detail report stock baru
- **Report stock details updated**: Jumlah detail report stock yang diupdate
- **Errors**: Jumlah error yang terjadi
- **Skipped outlets**: Outlet yang dilewati karena tidak ditemukan

## Support

Jika mengalami masalah dengan fitur import, silakan:
1. Periksa format file Excel sesuai template
2. Pastikan semua data wajib sudah diisi
3. Periksa log sistem untuk detail error
4. Hubungi administrator sistem jika diperlukan
