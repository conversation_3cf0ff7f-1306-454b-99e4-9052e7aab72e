# Report Stock Import Documentation

## Overview

Fitur import Excel untuk report stock yang memungkinkan import data laporan stok dalam jumlah besar dengan optimisasi untuk menghindari N+1 problem dan mendukung ratusan ribu baris data.

## Features

### 1. **Optimized Import Process**
- **Chunk Processing**: Data diproses dalam chunk untuk menghindari memory overflow
- **Batch Operations**: Menggunakan upsert operations untuk performa optimal
- **N+1 Prevention**: Pre-loading data untuk menghindari query berulang
- **Memory Management**: Garbage collection dan memory monitoring
- **Transaction Management**: Transaction per chunk untuk mengurangi lock time

### 2. **Smart Data Handling**
- **Auto Product Creation**: Membuat produk baru jika belum ada berdasarkan barcode
- **Product Updates**: Update nama, pack quantity, dan unit jika ada perubahan
- **Outlet Product Relations**: Membuat/update relasi product ke outlet dengan pareto
- **Report Stock Management**: Membuat/update report stock berdasarkan tanggal
- **Data Validation**: Validasi komprehensif dengan error reporting

### 3. **Excel Format Support**
- **Flexible Column Mapping**: Mendukung format kolom sesuai requirement
- **Numeric Parsing**: Support koma sebagai pemisah desimal
- **Error Handling**: Detailed error reporting dengan nomor baris
- **Template Download**: Template Excel dengan format yang benar

## Excel Format

### Required Columns

| Kolom | Field Name | Type | Required | Description |
|-------|------------|------|----------|-------------|
| OUTLET | outlet | String | Yes | Kode outlet yang sudah ada di sistem |
| NAMA PRODUK | nama_produk | String | Yes | Nama produk |
| PRT | prt | String | No | Outlet pareto (A/B/C) |
| BARCODE | barcode | String | Yes | Barcode produk (unique identifier) |
| PACK | pack | Numeric | No | Pack quantity (default: 1) |
| QTY | qty | Numeric | Yes | Quantity report stock |
| SAT | sat | String | No | Nama satuan unit (default: 'pcs') |

### Example Data

```
OUTLET  | NAMA PRODUK        | PRT | BARCODE    | PACK | QTY | SAT
AK001   | Paracetamol 500mg  | A   | PAR500001  | 10   | 100 | Tablet
AK001   | Amoxicillin 500mg  | B   | AMX500001  | 1    | 50  | Capsule
AK002   | Paracetamol 500mg  | A   | PAR500001  | 10   | 75  | Tablet
```

## Import Logic

### 1. **Report Date Validation**
- Cek data report stock berdasarkan tanggal yang dipilih
- Jika ada, ambil ID report stock untuk update data
- Jika tidak ada, buat data report stock baru

### 2. **Outlet Validation**
- Cek kode outlet di database
- Jika tidak ada, lewatkan baris dan laporkan sebagai skipped outlet
- Outlet yang tidak ditemukan akan dicatat dalam summary

### 3. **Product Management**
- Cek product berdasarkan barcode
- Jika ada: update nama product, pack quantity, dan satuan unit
- Jika tidak ada: buat product baru dengan data dari Excel

### 4. **Outlet Product Relations**
- Cek relasi outlet-product berdasarkan outlet_id dan product_id
- Jika ada: update outlet pareto
- Jika tidak ada: buat relasi baru dengan outlet pareto

### 5. **Report Stock Details**
- Cek report stock detail berdasarkan report_stock_id, outlet_id, dan product_id
- Jika ada: update quantity
- Jika tidak ada: buat data report stock detail baru

## Performance Optimizations

### 1. **Database Optimizations**
- **Upsert Operations**: Menggunakan upsert untuk insert/update dalam satu operasi
- **Batch Processing**: Memproses data dalam batch untuk mengurangi round trips
- **Index Usage**: Memanfaatkan index pada unique columns
- **Transaction Per Chunk**: Mengurangi lock time dengan transaction per chunk

### 2. **Memory Management**
- **Chunk Processing**: Data diproses dalam chunk konfigurabel
- **Cache Management**: Pre-loading data untuk menghindari N+1 queries
- **Garbage Collection**: Force garbage collection setelah setiap chunk
- **Memory Monitoring**: Real-time memory usage monitoring

### 3. **Configuration**
```php
// config/import.php
'report_stocks' => [
    'chunk_size' => 500,           // Rows per chunk
    'batch_size' => 500,           // Batch size for operations
    'memory_limit' => '1G',        // Memory limit
    'max_execution_time' => 0,     // No time limit
    'enable_logging' => true,      // Enable progress logging
    'database' => [
        'disable_foreign_key_checks' => false,
        'disable_query_log' => true,
        'use_transactions' => true,
    ],
],
```

## Usage

### 1. **Via Filament UI**
1. Navigate to Report Stocks page
2. Click "Import Excel" button
3. Select report date
4. Upload Excel file
5. Click "Import" to start process

### 2. **Programmatic Usage**
```php
use App\Imports\ReportStockImport;
use Maatwebsite\Excel\Facades\Excel;

$reportDate = '2025-09-16';
$import = new ReportStockImport($reportDate);
Excel::import($import, $filePath);

// Get statistics
$summary = $import->getImportSummary();
$errors = $import->getErrors();
$skippedOutlets = $import->getSkippedOutlets();
```

### 3. **Template Download**
- Click "Download Template" button to get Excel template
- Template includes proper column headers and sample data
- Use template to ensure correct format

## Error Handling

### Common Errors
1. **Missing Required Fields**: Outlet, barcode, product name, or quantity
2. **Invalid Outlet**: Outlet code not found in database
3. **Invalid Data Types**: Non-numeric values for quantity or pack
4. **File Format**: Unsupported file format or corrupted file

### Error Reporting
- Errors are collected with row numbers and descriptions
- Detailed error messages in import summary
- Skipped outlets are tracked separately
- Failed rows don't stop the entire import process

## Performance Benchmarks

### Expected Performance (typical server)
- **1,000 rows**: ~2-4 seconds
- **5,000 rows**: ~8-15 seconds
- **10,000 rows**: ~15-30 seconds
- **50,000 rows**: ~80-150 seconds
- **100,000 rows**: ~160-300 seconds

### Memory Usage
- **Base memory**: ~100MB
- **Per 1000 rows**: ~10-15MB additional
- **Peak for 100K rows**: ~800MB-1.2GB

## Testing

### 1. **Unit Tests**
```bash
php artisan test tests/Feature/ReportStockImportTest.php
```

### 2. **Manual Testing**
```bash
php scripts/test_report_stock_import_manual.php
```

### 3. **Performance Testing**
```bash
php scripts/test_report_stock_import_performance.php [max_rows]
```

## Monitoring

### Progress Logging
```php
// Enable detailed logging
config(['import.report_stocks.enable_logging' => true]);

// Check logs
tail -f storage/logs/laravel.log | grep "Import progress"
```

### Statistics Available
- Processed rows count
- Products created/updated
- Outlet products created/updated  
- Report stock details created/updated
- Error count and details
- Skipped outlets list
- Execution time and memory usage

## Best Practices

1. **Use Template**: Always download and use the latest template
2. **Validate Data**: Check data format before import
3. **Test Small**: Test with small datasets first
4. **Monitor Performance**: Watch memory and execution time
5. **Backup Data**: Backup before large imports
6. **Check Results**: Verify imported data accuracy
7. **Handle Errors**: Review and fix errors before re-importing

## Troubleshooting

### Memory Issues
- Reduce chunk_size in configuration
- Increase PHP memory_limit
- Process data in smaller batches

### Performance Issues
- Check database indexes
- Optimize chunk_size configuration
- Enable query log temporarily to identify slow queries

### Import Failures
- Check file format and encoding
- Verify required columns exist
- Ensure outlet codes exist in database
- Check for duplicate barcodes in file
