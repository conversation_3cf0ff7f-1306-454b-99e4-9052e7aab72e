<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use App\Models\Outlet;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use Filament\Resources\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions\Action;
use Carbon\Carbon;

class OutletSelectionByDate extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = ReportStockResource::class;
    protected static ?string $title = 'Select Outlet';

    public $date;
    public $reportDate;
    public $reportStock;

    public function mount($date): void
    {
        $this->date = $date;
        $this->reportDate = Carbon::parse($date);
        $this->reportStock = ReportStock::where('report_date', $this->reportDate)->firstOrFail();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('code')
                    ->label('Outlet Code')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->copyable(),

                TextColumn::make('name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => "Code: {$record->code} • Click to view products")
                    ->weight('medium')
                    ->tooltip('Click to view product details for this outlet'),

                TextColumn::make('total_products')
                    ->label('Products')
                    ->badge()
                    ->color('primary')
                    ->getStateUsing(function ($record) {
                        return ReportStockDetail::where('outlet_id', $record->id)
                            ->whereHas('reportStock', function ($q) {
                                $q->where('report_date', $this->reportDate);
                            })
                            ->count();
                    })
                    ->description('Products reported'),

                TextColumn::make('total_quantity')
                    ->label('Total Quantity')
                    ->badge()
                    ->color('success')
                    ->getStateUsing(function ($record) {
                        return number_format(
                            ReportStockDetail::where('outlet_id', $record->id)
                                ->whereHas('reportStock', function ($q) {
                                    $q->where('report_date', $this->reportDate);
                                })
                                ->sum('quantity')
                        );
                    })
                    ->description('Sum of quantities'),

                TextColumn::make('low_stock_count')
                    ->label('Low Stock')
                    ->badge()
                    ->color('warning')
                    ->getStateUsing(function ($record) {
                        // Count products with quantity <= 10 (example threshold)
                        return ReportStockDetail::where('outlet_id', $record->id)
                            ->whereHas('reportStock', function ($q) {
                                $q->where('report_date', $this->reportDate);
                            })
                            ->where('quantity', '<=', 10)
                            ->count();
                    })
                    ->description('Products with low stock'),

                TextColumn::make('out_of_stock_count')
                    ->label('Out of Stock')
                    ->badge()
                    ->color('danger')
                    ->getStateUsing(function ($record) {
                        return ReportStockDetail::where('outlet_id', $record->id)
                            ->whereHas('reportStock', function ($q) {
                                $q->where('report_date', $this->reportDate);
                            })
                            ->where('quantity', 0)
                            ->count();
                    })
                    ->description('Products out of stock'),
            ])
            ->recordAction('viewDetails')
            ->recordUrl(fn ($record) => static::$resource::getUrl('details', [
                'outlet' => $record->id,
                'date' => $this->date
            ]))
            ->defaultSort('name')
            ->searchPlaceholder('Search outlets...')
            ->emptyStateHeading('No Outlets Found')
            ->emptyStateDescription("No outlets have reports for {$this->reportDate->format('M j, Y')}.")
            ->emptyStateIcon('heroicon-o-building-storefront')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    protected function getTableQuery(): Builder
    {
        // Get outlets that have report stock details for this date
        return Outlet::query()
            ->whereHas('reportStockDetails', function ($query) {
                $query->whereHas('reportStock', function ($q) {
                    $q->where('report_date', $this->reportDate);
                });
            })
            ->orderBy('name');
    }

    public function getTitle(): string
    {
        return "Select Outlet - {$this->reportDate->format('M j, Y')}";
    }

    public function getHeading(): string
    {
        return "Outlets with Stock Reports";
    }

    public function getSubheading(): string
    {
        return "Report Date: {$this->reportDate->format('l, F j, Y')}";
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back_to_dates')
                ->label('Back to Dates')
                ->icon('heroicon-m-arrow-left')
                ->color('gray')
                ->url(static::$resource::getUrl('index')),
        ];
    }

    public function getView(): string
    {
        return 'filament.resources.report-stocks.pages.outlet-selection-by-date';
    }
}
