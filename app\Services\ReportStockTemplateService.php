<?php

namespace App\Services;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use App\Models\Outlet;

class ReportStockTemplateService
{
    /**
     * Generate Excel template for report stock import
     */
    public function generateTemplate(): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Report Stock Template');

        // Set headers
        $headers = [
            'OUTLET' => 'Kode outlet (contoh: AK001)',
            'NAMA PRODUK' => 'Nama produk',
            'PRT' => 'Outlet pareto (A/B/C)',
            'BARCODE' => 'Barcode produk',
            'PACK' => 'Pack quantity',
            'QTY' => 'Quantity stock',
            'SAT' => 'Satuan unit (contoh: Tablet, Capsule, Botol)',
        ];

        $columnIndex = 1;
        foreach ($headers as $header => $description) {
            $cell = $sheet->getCellByColumnAndRow($columnIndex, 1);
            $cell->setValue($header);

            // Note: Comments removed due to PhpSpreadsheet compatibility
            // Description: $description

            $columnIndex++;
        }

        // Style headers
        $headerRange = 'A1:G1';
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
                'size' => 12,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Add sample data
        $sampleData = $this->getSampleData();
        $row = 2;
        foreach ($sampleData as $data) {
            $sheet->fromArray([$data], null, 'A' . $row);
            $row++;
        }

        // Style sample data
        if (!empty($sampleData)) {
            $dataRange = 'A2:G' . ($row - 1);
            $sheet->getStyle($dataRange)->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ]);

            // Style sample data with light background
            $sheet->getStyle($dataRange)->applyFromArray([
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'],
                ],
            ]);
        }

        // Auto-size columns
        foreach (range('A', 'G') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Set minimum column widths
        $sheet->getColumnDimension('A')->setWidth(12); // OUTLET
        $sheet->getColumnDimension('B')->setWidth(25); // NAMA PRODUK
        $sheet->getColumnDimension('C')->setWidth(8);  // PRT
        $sheet->getColumnDimension('D')->setWidth(15); // BARCODE
        $sheet->getColumnDimension('E')->setWidth(8);  // PACK
        $sheet->getColumnDimension('F')->setWidth(10); // QTY
        $sheet->getColumnDimension('G')->setWidth(15); // SAT

        // Add instructions sheet
        $this->addInstructionsSheet($spreadsheet);

        // Add outlets reference sheet
        $this->addOutletsSheet($spreadsheet);

        // Save to temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'report_stock_template_') . '.xlsx';
        $writer = new Xlsx($spreadsheet);
        $writer->save($tempFile);

        return $tempFile;
    }

    /**
     * Get sample data for template
     */
    protected function getSampleData(): array
    {
        // Get first few outlets for sample
        $outlets = Outlet::limit(3)->get();
        
        if ($outlets->isEmpty()) {
            return [
                ['AK001', 'Paracetamol 500mg', 'A', 'PAR500001', 10, 100, 'Tablet'],
                ['AK001', 'Amoxicillin 500mg', 'B', 'AMX500001', 1, 50, 'Capsule'],
                ['AK002', 'Vitamin C 1000mg', 'A', 'VTC1000001', 1, 75, 'Tablet'],
            ];
        }

        $sampleData = [];
        $sampleProducts = [
            ['Paracetamol 500mg', 'A', 'PAR500001', 10, 100, 'Tablet'],
            ['Amoxicillin 500mg', 'B', 'AMX500001', 1, 50, 'Capsule'],
            ['Vitamin C 1000mg', 'A', 'VTC1000001', 1, 75, 'Tablet'],
        ];

        foreach ($outlets as $index => $outlet) {
            if (isset($sampleProducts[$index])) {
                $product = $sampleProducts[$index];
                $sampleData[] = [
                    $outlet->code,
                    $product[0], // name
                    $product[1], // pareto
                    $product[2], // barcode
                    $product[3], // pack
                    $product[4], // qty
                    $product[5], // unit
                ];
            }
        }

        return $sampleData;
    }

    /**
     * Add instructions sheet
     */
    protected function addInstructionsSheet(Spreadsheet $spreadsheet): void
    {
        $instructionsSheet = $spreadsheet->createSheet();
        $instructionsSheet->setTitle('Instructions');

        $instructions = [
            ['PANDUAN IMPORT REPORT STOCK', ''],
            ['', ''],
            ['1. Format File:', ''],
            ['   - Gunakan format Excel (.xlsx atau .xls)', ''],
            ['   - Maksimal ukuran file: 10MB', ''],
            ['   - Gunakan sheet pertama untuk data', ''],
            ['', ''],
            ['2. Kolom yang Diperlukan:', ''],
            ['   OUTLET', 'Kode outlet yang sudah ada di sistem'],
            ['   NAMA PRODUK', 'Nama produk (akan dibuat/diupdate)'],
            ['   PRT', 'Outlet pareto (A/B/C, opsional)'],
            ['   BARCODE', 'Barcode produk (unique identifier)'],
            ['   PACK', 'Pack quantity (default: 1)'],
            ['   QTY', 'Quantity stock (wajib diisi)'],
            ['   SAT', 'Satuan unit (default: pcs)'],
            ['', ''],
            ['3. Aturan Import:', ''],
            ['   - Jika outlet tidak ditemukan, baris akan dilewati', ''],
            ['   - Jika produk belum ada, akan dibuat baru', ''],
            ['   - Jika produk sudah ada, akan diupdate', ''],
            ['   - Data akan dikelompokkan berdasarkan tanggal report', ''],
            ['', ''],
            ['4. Tips:', ''],
            ['   - Pastikan kode outlet benar dan sudah ada', ''],
            ['   - Gunakan barcode yang unik untuk setiap produk', ''],
            ['   - Quantity harus berupa angka positif', ''],
            ['   - Gunakan koma (,) sebagai pemisah desimal jika perlu', ''],
            ['', ''],
            ['5. Setelah Import:', ''],
            ['   - Periksa summary hasil import', ''],
            ['   - Review error jika ada', ''],
            ['   - Verifikasi data yang telah diimport', ''],
        ];

        $row = 1;
        foreach ($instructions as $instruction) {
            $instructionsSheet->setCellValue('A' . $row, $instruction[0]);
            $instructionsSheet->setCellValue('B' . $row, $instruction[1]);
            $row++;
        }

        // Style title
        $instructionsSheet->getStyle('A1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 16,
                'color' => ['rgb' => '2F5597'],
            ],
        ]);

        // Style section headers
        foreach ([3, 8, 17, 24, 30] as $headerRow) {
            $instructionsSheet->getStyle('A' . $headerRow)->applyFromArray([
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => '2F5597'],
                ],
            ]);
        }

        // Auto-size columns
        $instructionsSheet->getColumnDimension('A')->setWidth(30);
        $instructionsSheet->getColumnDimension('B')->setWidth(50);
    }

    /**
     * Add outlets reference sheet
     */
    protected function addOutletsSheet(Spreadsheet $spreadsheet): void
    {
        $outletsSheet = $spreadsheet->createSheet();
        $outletsSheet->setTitle('Outlets Reference');

        // Headers
        $outletsSheet->setCellValue('A1', 'KODE OUTLET');
        $outletsSheet->setCellValue('B1', 'NAMA OUTLET');

        // Style headers
        $outletsSheet->getStyle('A1:B1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        // Get outlets data
        $outlets = Outlet::orderBy('code')->get();
        $row = 2;
        foreach ($outlets as $outlet) {
            $outletsSheet->setCellValue('A' . $row, $outlet->code);
            $outletsSheet->setCellValue('B' . $row, $outlet->name);
            $row++;
        }

        // Auto-size columns
        $outletsSheet->getColumnDimension('A')->setAutoSize(true);
        $outletsSheet->getColumnDimension('B')->setAutoSize(true);

        // Set minimum widths
        $outletsSheet->getColumnDimension('A')->setWidth(15);
        $outletsSheet->getColumnDimension('B')->setWidth(30);
    }
}
