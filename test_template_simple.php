<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Testing template generation...\n";
    
    $service = new App\Services\ReportStockTemplateService();
    $path = $service->generateTemplate();
    
    echo "Template generated: $path\n";
    
    if (file_exists($path)) {
        echo "File size: " . filesize($path) . " bytes\n";
        echo "✅ Template generation successful!\n";
        unlink($path); // Clean up
    } else {
        echo "❌ Template file not found!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
