<?php

namespace App\Filament\Resources\ReportStocks\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ReportStocksTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('report_date')
                    ->label('Report Date')
                    ->date('M j, Y')
                    ->sortable()
                    ->badge()
                    ->color('primary')
                    ->description(fn ($record) => $record->report_date->diffForHumans() . ' • Click to view outlets')
                    ->tooltip('Click to view outlets for this report date'),

                TextColumn::make('total_outlets')
                    ->label('Outlets')
                    ->badge()
                    ->color('info')
                    ->getStateUsing(fn ($record) => $record->getTotalOutlets())
                    ->description('Number of outlets'),

                TextColumn::make('total_products')
                    ->label('Products')
                    ->badge()
                    ->color('warning')
                    ->getStateUsing(fn ($record) => $record->getTotalProducts())
                    ->description('Total products'),

                TextColumn::make('total_quantity')
                    ->label('Total Quantity')
                    ->numeric()
                    ->badge()
                    ->color('success')
                    ->getStateUsing(fn ($record) => $record->getTotalQuantity())
                    ->description('Total stock quantity'),

                TextColumn::make('is_generated')
                    ->label('Status')
                    ->badge()
                    ->color(fn ($record) => $record->is_generated ? 'success' : 'warning')
                    ->formatStateUsing(fn ($record) => $record->is_generated ? 'Generated' : 'Pending')
                    ->description('Purchase request generation status'),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->created_at->diffForHumans()),

                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->updated_at->diffForHumans()),
            ])
            ->filters([
                // No filters for now since we're showing report dates, not individual records
            ])
            ->recordUrl(fn ($record) => route('filament.admin.resources.report-stocks.outlets', [
                'date' => $record->report_date->format('Y-m-d')
            ]))
            ->recordActions([
                EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-m-pencil-square')
                    ->color('warning'),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('Delete Selected Reports')
                        ->modalDescription('Are you sure you want to delete the selected stock reports? This action cannot be undone.')
                        ->modalSubmitActionLabel('Yes, delete them'),
                ]),
            ])
            ->defaultSort('report_date', 'desc')
            ->searchPlaceholder('Search by outlet or product name...')
            ->emptyStateHeading('No stock reports found')
            ->emptyStateDescription('Create your first stock report to get started.')
            ->emptyStateIcon('heroicon-o-clipboard-document-list')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
