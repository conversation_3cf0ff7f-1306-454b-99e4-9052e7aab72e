<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING OUTLET DATA FOR DATE ===\n\n";

$testDate = '2025-09-16';
echo "Testing for date: {$testDate}\n\n";

// Test 1: Check if ReportStock exists for this date
echo "1. Checking ReportStock for date {$testDate}:\n";
$reportStock = App\Models\ReportStock::where('report_date', $testDate)->first();

if ($reportStock) {
    echo "   ✅ ReportStock found: ID {$reportStock->id}\n";
    echo "   - Report Date: {$reportStock->report_date}\n";
    echo "   - Created: {$reportStock->created_at}\n";
} else {
    echo "   ❌ No ReportStock found for this date\n";
    echo "   Creating ReportStock...\n";
    $reportStock = App\Models\ReportStock::create(['report_date' => $testDate]);
    echo "   ✅ Created ReportStock: ID {$reportStock->id}\n";
}

// Test 2: Check ReportStockDetails for this ReportStock
echo "\n2. Checking ReportStockDetails:\n";
$details = App\Models\ReportStockDetail::where('report_stock_id', $reportStock->id)->get();

echo "   Found {$details->count()} report stock details\n";

if ($details->isEmpty()) {
    echo "   ❌ No ReportStockDetails found. Creating test data...\n";
    
    // Get some outlets and products
    $outlets = App\Models\Outlet::take(3)->get();
    $products = App\Models\Product::take(3)->get();
    
    if ($outlets->isEmpty()) {
        echo "   ❌ No outlets found in database\n";
        return;
    }
    
    if ($products->isEmpty()) {
        echo "   ❌ No products found in database\n";
        return;
    }
    
    echo "   Creating test data with {$outlets->count()} outlets and {$products->count()} products...\n";
    
    foreach ($outlets as $outlet) {
        foreach ($products as $product) {
            App\Models\ReportStockDetail::create([
                'report_stock_id' => $reportStock->id,
                'outlet_id' => $outlet->id,
                'product_id' => $product->id,
                'quantity' => rand(10, 100)
            ]);
        }
    }
    
    $details = App\Models\ReportStockDetail::where('report_stock_id', $reportStock->id)->get();
    echo "   ✅ Created {$details->count()} report stock details\n";
}

// Test 3: Check outlets that have data for this date
echo "\n3. Checking outlets with data for this date:\n";

$outletsWithData = App\Models\Outlet::whereHas('reportStockDetails', function ($query) use ($reportStock) {
    $query->where('report_stock_id', $reportStock->id);
})->get();

echo "   Found {$outletsWithData->count()} outlets with data:\n";

foreach ($outletsWithData as $outlet) {
    $productCount = App\Models\ReportStockDetail::where('report_stock_id', $reportStock->id)
        ->where('outlet_id', $outlet->id)
        ->count();
    
    $totalQuantity = App\Models\ReportStockDetail::where('report_stock_id', $reportStock->id)
        ->where('outlet_id', $outlet->id)
        ->sum('quantity');
    
    echo "   - {$outlet->code} ({$outlet->name}): {$productCount} products, {$totalQuantity} total qty\n";
}

// Test 4: Test the exact query used by OutletSelectionByDate
echo "\n4. Testing OutletSelectionByDate query:\n";

$reportDate = \Carbon\Carbon::parse($testDate);

$queryResult = App\Models\Outlet::query()
    ->whereHas('reportStockDetails', function ($query) use ($reportDate) {
        $query->whereHas('reportStock', function ($q) use ($reportDate) {
            $q->where('report_date', $reportDate);
        });
    })
    ->orderBy('name')
    ->get();

echo "   Query returned {$queryResult->count()} outlets:\n";

foreach ($queryResult as $outlet) {
    echo "   - {$outlet->code} ({$outlet->name})\n";
}

// Test 5: Check relationships
echo "\n5. Testing relationships:\n";

// Check if Outlet has reportStockDetails relationship
if (method_exists(App\Models\Outlet::class, 'reportStockDetails')) {
    echo "   ✅ Outlet::reportStockDetails() relationship exists\n";
} else {
    echo "   ❌ Outlet::reportStockDetails() relationship missing\n";
}

// Check if ReportStockDetail has reportStock relationship
if (method_exists(App\Models\ReportStockDetail::class, 'reportStock')) {
    echo "   ✅ ReportStockDetail::reportStock() relationship exists\n";
} else {
    echo "   ❌ ReportStockDetail::reportStock() relationship missing\n";
}

// Test the relationship directly
$firstOutlet = App\Models\Outlet::first();
if ($firstOutlet) {
    $relatedDetails = $firstOutlet->reportStockDetails()->count();
    echo "   ✅ First outlet ({$firstOutlet->name}) has {$relatedDetails} related details\n";
}

echo "\n=== SUMMARY ===\n";
echo "ReportStock ID: {$reportStock->id}\n";
echo "Report Date: {$reportStock->report_date}\n";
echo "Total Details: {$details->count()}\n";
echo "Outlets with Data: {$outletsWithData->count()}\n";
echo "Query Result: {$queryResult->count()}\n";

if ($queryResult->count() > 0) {
    echo "\n✅ Data is available - table should display outlets\n";
} else {
    echo "\n❌ No data found - table will be empty\n";
}
