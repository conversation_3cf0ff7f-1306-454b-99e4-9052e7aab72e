<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($heading); ?></h2>
                <?php if($view_type === 'dates'): ?>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Click on any date to view outlets with purchase requests on that date.</p>
                <?php elseif($view_type === 'outlets'): ?>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Click on any outlet to view the products requested on this date.</p>
                    <div class="mt-2">
                        <a href="<?php echo e(\App\Filament\Pages\PurchaseRequestReports::getUrl()); ?>"
                           class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            ← Back to Dates
                        </a>
                    </div>
                <?php else: ?>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Detailed list of all products requested with their quantities and specifications.</p>
                    <div class="mt-2">
                        <a href="<?php echo e(\App\Filament\Pages\PurchaseRequestReports::getUrl(['view' => 'outlets', 'date' => $date])); ?>"
                           class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            ← Back to Outlets
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <?php if($view_type === 'dates'): ?>
                <?php echo $__env->make('filament.pages.partials.dates-table', ['data' => $reportData], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php elseif($view_type === 'outlets'): ?>
                <?php echo $__env->make('filament.pages.partials.outlets-table', ['data' => $reportData, 'date' => $date], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php else: ?>
                <?php echo $__env->make('filament.pages.partials.products-table', ['data' => $reportData], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\apotek\resources\views\filament\pages\purchase-request-reports.blade.php ENDPATH**/ ?>