<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Imports\ReportStockImport;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Services\ReportStockTemplateService;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class TestReportStockImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:report-stock-import {--size=small : Test size (small, medium, large)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Report Stock Import functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Report Stock Import Test ===');
        $this->newLine();

        // Test 1: Check if classes exist
        $this->info('1. Checking if classes exist...');
        $this->checkClasses();
        $this->newLine();

        // Test 2: Create test outlets
        $this->info('2. Preparing test outlets...');
        $testOutlets = $this->createTestOutlets();
        $this->newLine();

        // Test 3: Test template generation
        $this->info('3. Testing template generation...');
        $this->testTemplateGeneration();
        $this->newLine();

        // Test 4: Test import functionality
        $this->info('4. Testing import functionality...');
        $this->testImportFunctionality($testOutlets);
        $this->newLine();

        $this->info('✅ All tests completed!');
    }

    private function checkClasses()
    {
        $classes = [
            'App\\Services\\ImportOptimizationService',
            'App\\Imports\\ReportStockImport',
            'App\\Services\\ReportStockTemplateService',
        ];

        foreach ($classes as $class) {
            if (class_exists($class)) {
                $this->line("   ✅ {$class} - EXISTS");
            } else {
                $this->error("   ❌ {$class} - NOT FOUND");
            }
        }
    }

    private function createTestOutlets()
    {
        $testOutlets = [
            ['code' => 'AK001', 'name' => 'Apotek Keluarga 001'],
            ['code' => 'AK002', 'name' => 'Apotek Keluarga 002'],
            ['code' => 'AK003', 'name' => 'Apotek Keluarga 003'],
        ];

        $outlets = [];
        foreach ($testOutlets as $outletData) {
            $outlet = Outlet::firstOrCreate(
                ['code' => $outletData['code']],
                ['name' => $outletData['name']]
            );
            $outlets[] = $outlet;
            $this->line("   ✅ Outlet {$outlet->code} - {$outlet->name}");
        }

        return $outlets;
    }

    private function testTemplateGeneration()
    {
        try {
            $templateService = new ReportStockTemplateService();
            $templatePath = $templateService->generateTemplate();

            if (file_exists($templatePath)) {
                $this->line("   ✅ Template generated successfully");
                $fileSize = filesize($templatePath);
                $this->line("   📄 Template size: " . number_format($fileSize) . " bytes");
                unlink($templatePath); // Clean up
            } else {
                $this->error("   ❌ Template file not found");
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Template generation failed: " . $e->getMessage());
        }
    }

    private function testImportFunctionality($outlets)
    {
        // Create test Excel file
        $testFile = $this->createTestExcelFile($outlets);

        try {
            $reportDate = now()->format('Y-m-d');

            // Clean up any existing data
            $existingReport = ReportStock::where('report_date', $reportDate)->first();
            if ($existingReport) {
                $this->line("   🧹 Cleaning up existing test data...");
                $existingReport->details()->delete();
                $existingReport->delete();
            }

            // Run import
            $this->line("   📥 Starting import for date: {$reportDate}");
            $startTime = microtime(true);

            $import = new ReportStockImport($reportDate);
            Excel::import($import, $testFile);

            $endTime = microtime(true);
            $importTime = $endTime - $startTime;

            // Get results
            $summary = $import->getImportSummary();
            $errors = $import->getErrors();

            $this->line("   ✅ Import completed in " . round($importTime, 2) . " seconds");

            // Display results
            $this->line("   📊 Statistics:");
            $this->line("      - Processed rows: {$summary['processed_rows']}");
            $this->line("      - Products created: {$summary['products_created']}");
            $this->line("      - Products updated: {$summary['products_updated']}");
            $this->line("      - Outlet products created: {$summary['outlet_products_created']}");
            $this->line("      - Outlet products updated: {$summary['outlet_products_updated']}");
            $this->line("      - Report details created: {$summary['report_stock_details_created']}");
            $this->line("      - Report details updated: {$summary['report_stock_details_updated']}");
            $this->line("      - Errors: " . count($errors));

            if (!empty($errors)) {
                $this->warn("   ⚠️  Errors encountered:");
                foreach (array_slice($errors, 0, 3) as $error) {
                    $this->line("      - Row {$error['row']}: {$error['error']}");
                }
            }

            if (!empty($summary['skipped_outlets'])) {
                $this->warn("   ⚠️  Skipped outlets: " . implode(', ', $summary['skipped_outlets']));
            }

            // Verify data
            $this->verifyImportedData($reportDate);

        } catch (\Exception $e) {
            $this->error("   ❌ Import failed: " . $e->getMessage());
        } finally {
            // Clean up test file
            if (file_exists($testFile)) {
                unlink($testFile);
            }
        }
    }

    private function createTestExcelFile($outlets)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Headers
        $headers = ['OUTLET', 'NAMA PRODUK', 'PRT', 'BARCODE', 'PACK', 'QTY', 'SAT'];
        $sheet->fromArray([$headers], null, 'A1');

        // Test data
        $testData = [
            [$outlets[0]->code, 'Paracetamol 500mg', 'A', 'PAR500001', 10, 100, 'Tablet'],
            [$outlets[0]->code, 'Amoxicillin 500mg', 'B', 'AMX500001', 1, 50, 'Capsule'],
            [$outlets[1]->code, 'Vitamin C 1000mg', 'A', 'VTC1000001', 1, 75, 'Tablet'],
            [$outlets[1]->code, 'Paracetamol 500mg', 'A', 'PAR500001', 10, 120, 'Tablet'], // Same product, different outlet
            [$outlets[2]->code, 'Ibuprofen 400mg', 'B', 'IBU400001', 1, 80, 'Tablet'],
            ['INVALID', 'Invalid Product', 'A', 'INV001', 1, 10, 'Tablet'], // Invalid outlet - should be skipped
        ];

        $row = 2;
        foreach ($testData as $data) {
            $sheet->fromArray([$data], null, 'A' . $row);
            $row++;
        }

        // Save test file
        $testFile = storage_path('app/test_import_' . time() . '.xlsx');
        $writer = new Xlsx($spreadsheet);
        $writer->save($testFile);

        return $testFile;
    }

    private function verifyImportedData($reportDate)
    {
        $this->line("   🔍 Verifying imported data...");

        $reportStock = ReportStock::where('report_date', $reportDate)->first();
        if ($reportStock) {
            $this->line("   ✅ Report Stock created with ID: {$reportStock->id}");

            $detailsCount = $reportStock->details()->count();
            $this->line("   ✅ Report Stock Details count: {$detailsCount}");

            // Check specific products
            $paracetamolProduct = Product::where('barcode', 'PAR500001')->first();
            if ($paracetamolProduct) {
                $this->line("   ✅ Paracetamol product: {$paracetamolProduct->name}");

                $paracetamolDetails = ReportStockDetail::where('report_stock_id', $reportStock->id)
                    ->where('product_id', $paracetamolProduct->id)
                    ->with('outlet')
                    ->get();

                $this->line("   ✅ Paracetamol details count: " . $paracetamolDetails->count());
                foreach ($paracetamolDetails as $detail) {
                    $this->line("      - {$detail->outlet->code}: {$detail->quantity} units");
                }
            }
        } else {
            $this->error("   ❌ Report Stock not found!");
        }
    }
}
