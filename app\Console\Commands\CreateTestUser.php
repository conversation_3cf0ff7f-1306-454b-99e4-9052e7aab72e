<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:test-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user for development';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = '<EMAIL>';
        $password = 'password';

        $user = User::firstOrCreate(
            ['email' => $email],
            [
                'name' => 'Test Admin',
                'password' => Hash::make($password),
            ]
        );

        $this->info("Test user created/found:");
        $this->info("Email: {$email}");
        $this->info("Password: {$password}");
        $this->info("User ID: {$user->id}");
    }
}
