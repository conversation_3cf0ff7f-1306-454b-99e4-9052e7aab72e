<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockImport;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

echo "=== Manual Report Stock Import Test ===\n\n";

// Create test data if not exists
echo "Setting up test data...\n";

// Create outlets
$outlets = [
    ['name' => 'Apotek Keluarga 1', 'code' => 'AK001'],
    ['name' => 'Apotek Keluarga 2', 'code' => 'AK002'],
    ['name' => 'Apotek Keluarga 3', 'code' => 'AK003'],
];

foreach ($outlets as $outletData) {
    Outlet::firstOrCreate(['code' => $outletData['code']], $outletData);
}

echo "Created/verified " . count($outlets) . " outlets\n";

// Create test Excel file
$testFile = 'test_report_stock_import.xlsx';
$reportDate = '2025-09-16';

echo "Creating test Excel file: {$testFile}\n";

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$headers = ['OUTLET', 'NAMA PRODUK', 'PRT', 'BARCODE', 'PACK', 'QTY', 'SAT'];
$sheet->fromArray([$headers], null, 'A1');

// Create test data
$testData = [
    // New products
    ['AK001', 'Paracetamol 500mg', 'A', 'PAR500001', '10', 100, 'Tablet'],
    ['AK001', 'Amoxicillin 500mg', 'B', 'AMX500001', '1', 50, 'Capsule'],
    ['AK002', 'Paracetamol 500mg', 'A', 'PAR500001', '10', 75, 'Tablet'],
    ['AK002', 'Ibuprofen 400mg', 'C', 'IBU400001', '1', 25, 'Tablet'],
    ['AK003', 'Vitamin C 1000mg', 'B', 'VTC1000001', '1', 200, 'Tablet'],
    
    // Test with comma decimal separator
    ['AK001', 'Sirup Batuk', 'A', 'SBT001', '2,5', '15,5', 'Botol'],
    
    // Test missing outlet (should be skipped)
    ['NONEXISTENT', 'Invalid Product', 'A', 'INV001', '1', 10, 'PCS'],
    
    // Test empty required fields (should error)
    ['AK001', '', 'A', 'EMPTY001', '1', 10, 'PCS'], // Empty product name
    ['', 'Empty Outlet Product', 'A', 'EMPTY002', '1', 10, 'PCS'], // Empty outlet
    ['AK001', 'Empty Barcode Product', 'A', '', '1', 10, 'PCS'], // Empty barcode
];

// Add test data to sheet
$row = 2;
foreach ($testData as $data) {
    $sheet->fromArray([$data], null, 'A' . $row);
    $row++;
}

// Save Excel file
$writer = new Xlsx($spreadsheet);
$writer->save($testFile);

echo "Test file created with " . count($testData) . " data rows\n\n";

// Test import
echo "Starting import test...\n";
$startTime = microtime(true);
$startMemory = memory_get_usage(true);

try {
    $import = new ReportStockImport($reportDate);
    Excel::import($import, $testFile);
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage(true);
    $peakMemory = memory_get_peak_usage(true);
    
    echo "✅ Import completed successfully!\n\n";
    
    // Show statistics
    echo "=== Import Statistics ===\n";
    $summary = $import->getImportSummary();
    foreach ($summary as $key => $value) {
        if (is_array($value)) {
            echo "{$key}: " . implode(', ', $value) . "\n";
        } else {
            echo "{$key}: {$value}\n";
        }
    }
    echo "\n";
    
    // Show performance metrics
    echo "=== Performance Metrics ===\n";
    echo "Execution time: " . round($endTime - $startTime, 2) . " seconds\n";
    echo "Memory used: " . formatBytes($endMemory - $startMemory) . "\n";
    echo "Peak memory: " . formatBytes($peakMemory) . "\n\n";
    
    // Show errors if any
    $errors = $import->getErrors();
    if (!empty($errors)) {
        echo "=== Errors ===\n";
        foreach ($errors as $error) {
            echo "Row {$error['row']}: {$error['error']}\n";
            echo "Data: " . json_encode($error['data']) . "\n\n";
        }
    }
    
    // Verify database data
    echo "=== Database Verification ===\n";
    echo "Report stocks: " . ReportStock::count() . "\n";
    echo "Products: " . Product::count() . "\n";
    echo "Report stock details: " . ReportStockDetail::count() . "\n";
    
    // Show sample data
    echo "\n=== Sample Report Stock Details ===\n";
    $sampleDetails = ReportStockDetail::with(['outlet', 'product', 'reportStock'])
        ->limit(5)
        ->get();
    
    foreach ($sampleDetails as $detail) {
        echo "- {$detail->outlet->code}: {$detail->product->name} = {$detail->quantity} {$detail->product->unit}\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Clean up
if (file_exists($testFile)) {
    unlink($testFile);
    echo "\nTest file cleaned up.\n";
}

function formatBytes($bytes)
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

echo "\n=== Test completed ===\n";
