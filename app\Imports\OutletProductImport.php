<?php

namespace App\Imports;

use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OutletProductImport implements ToCollection, WithHeadingRow, WithValidation, WithBatchInserts, WithChunkReading, SkipsEmptyRows
{
    protected $processedRows = 0;
    protected $productsCreated = 0;
    protected $outletProductsCreated = 0;
    protected $outletProductsUpdated = 0;
    protected $errors = [];

    // Cache untuk menghindari query berulang
    protected $outletCache = [];
    protected $productCache = [];
    protected $outletProductCache = [];

    public function collection(Collection $rows)
    {
        // Pre-load semua data yang diperlukan untuk menghindari N+1 queries
        $this->preloadData($rows);


        DB::transaction(function () use ($rows) {
            $productsToCreate = [];

            foreach ($rows as $index => $row) {
                try {
                    $rowNumber = $index + 2; // +2 karena header row dan 0-based index

                    // Validasi outlet
                    if (!isset($this->outletCache[$row['outlet_code']])) {
                        $this->errors[] = "Outlet with code '{$row['outlet_code']}' not found on row {$rowNumber}";
                        continue;
                    }

                    // Cek apakah product sudah ada
                    if (!isset($this->productCache[$row['barcode']])) {
                        // Siapkan data product baru
                        $productsToCreate[] = [
                            'name' => $row['product_name'],
                            'barcode' => $row['barcode'],
                            'unit' => $row['unit'] ?? 'Pcs',
                            'pack_quantity' => (int) ($row['pack_quantity'] ?? 1),
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];

                        // Tambahkan ke cache sementara
                        $this->productCache[$row['barcode']] = (object) [
                            'id' => null, // akan diisi setelah insert
                            'name' => $row['product_name'],
                            'barcode' => $row['barcode'],
                            'unit' => $row['unit'] ?? 'Pcs',
                            'pack_quantity' => (int) ($row['pack_quantity'] ?? 1),
                        ];
                    }

                    $this->processedRows++;

                } catch (\Exception $e) {
                    $this->errors[] = "Error processing row {$rowNumber}: " . $e->getMessage();
                    Log::error('Error during outlet product import', [
                        'row' => $row->toArray(),
                        'error' => $e->getMessage(),
                        'user_id' => Auth::id(),
                    ]);
                }
            }

            $this->processOutletProducts($rows);
        });
    }

    protected function preloadData(Collection $rows)
    {
        // Pre-load semua outlets
        $outletCodes = $rows->pluck('outlet_code')->unique()->filter();
        $outlets = Outlet::whereIn('code', $outletCodes)->get();
        foreach ($outlets as $outlet) {
            $this->outletCache[$outlet->code] = $outlet;
        }

        // Pre-load semua products
        $barcodes = $rows->pluck('barcode')->unique()->filter();
        $products = Product::whereIn('barcode', $barcodes)->get();
        foreach ($products as $product) {
            $this->productCache[$product->barcode] = $product;
        }

        // Pre-load existing outlet products
        $outletIds = $outlets->pluck('id');
        $productIds = $products->pluck('id');
        $outletProducts = OutletProduct::whereIn('outlet_id', $outletIds)
            ->whereIn('product_id', $productIds)
            ->get();

        foreach ($outletProducts as $op) {
            $this->outletProductCache["{$op->outlet_id}_{$op->product_id}"] = $op;
        }
    }

    protected function batchInsertProducts(array $productsToCreate)
    {
        // Insert dalam batch untuk performa optimal
        $chunks = array_chunk($productsToCreate, 5000);

        foreach ($chunks as $chunk) {
            Product::insert($chunk);
            $this->productsCreated += count($chunk);
        }

        // Update cache dengan products yang baru dibuat
        $newProducts = Product::whereIn('barcode', array_column($productsToCreate, 'barcode'))->get();
        foreach ($newProducts as $product) {
            $this->productCache[$product->barcode] = $product;
        }
    }

    protected function processOutletProducts(Collection $rows)
    {
        $outletProductsToCreate = [];
        $outletProductsToUpdate = [];

        foreach ($rows as $index => $row) {
            try {
                $outlet = $this->outletCache[$row['outlet_code']] ?? null;
                $product = $this->productCache[$row['barcode']] ?? null;

                if (!$outlet || !$product) {
                    continue;
                }

                $cacheKey = "{$outlet->id}_{$product->id}";
                
                
                if (isset($this->outletProductCache[$cacheKey])) {
                    $outletProductData = [
                        'rumus_pareto' => $row['rumus_pareto'] ?? $this->outletProductCache[$cacheKey]->rumus_pareto,
                        'min_buffer' => (int) ($row['min_buffer']) ?? $this->outletProductCache[$cacheKey]->min_buffer,
                        'max_buffer' => (int) ($row['max_buffer']) ?? $this->outletProductCache[$cacheKey]->max_buffer,
                    ];
                    // Update existing
                    $outletProductsToUpdate[] = [
                        'id' => $this->outletProductCache[$cacheKey]->id,
                        'data' => $outletProductData
                    ];
                } else {
                    $outletProductData = [
                        'rumus_pareto' => $row['rumus_pareto'] ?? null,
                        'min_buffer' => (int) ($row['min_buffer']) ?? 0,
                        'max_buffer' => (int) ($row['max_buffer']) ?? 0,
                    ];
                    // Create new
                    $outletProductsToCreate[] = array_merge([
                        'outlet_id' => $outlet->id,
                        'product_id' => $product->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ], $outletProductData);
                }

            } catch (\Exception $e) {
                Log::error('Error processing outlet product', [
                    'row' => $row->toArray(),
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Batch insert outlet products baru
        if (!empty($outletProductsToCreate)) {
            $chunks = array_chunk($outletProductsToCreate, 5000);
            foreach ($chunks as $chunk) {
                OutletProduct::insert($chunk);
                $this->outletProductsCreated += count($chunk);
            }
        }

        // Batch update outlet products yang sudah ada
        if (!empty($outletProductsToUpdate)) {
            foreach ($outletProductsToUpdate as $update) {
                OutletProduct::where('id', $update['id'])->update($update['data']);
                $this->outletProductsUpdated++;
            }
        }
    }

    public function rules(): array
    {
        return [
            'outlet_code' => 'required|string',
            'barcode' => 'required|string',
            'product_name' => 'required|string',
            'unit' => 'nullable|string',
            'pack_quantity' => 'nullable',
            'rumus_pareto' => 'nullable',
            'min_buffer' => 'nullable',
            'max_buffer' => 'nullable',
        ];
    }

    public function batchSize(): int
    {
        return config('import.outlet_products.batch_size', 1000);
    }

    public function chunkSize(): int
    {
        return config('import.outlet_products.chunk_size', 1000);
    }

    // Getter methods for statistics
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    public function getProductsCreated(): int
    {
        return $this->productsCreated;
    }

    public function getOutletProductsCreated(): int
    {
        return $this->outletProductsCreated;
    }

    public function getOutletProductsUpdated(): int
    {
        return $this->outletProductsUpdated;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
