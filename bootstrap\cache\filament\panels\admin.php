<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.pages.purchase-request-reports' => 'App\\Filament\\Pages\\PurchaseRequestReports',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'app.filament.widgets.outlet-comparison-chart' => 'App\\Filament\\Widgets\\OutletComparisonChart',
    'app.filament.widgets.pareto-analysis-chart' => 'App\\Filament\\Widgets\\ParetoAnalysisChart',
    'app.filament.widgets.purchase-request-trends-chart' => 'App\\Filament\\Widgets\\PurchaseRequestTrendsChart',
    'app.filament.widgets.purchase-requests-overview-widget' => 'App\\Filament\\Widgets\\PurchaseRequestsOverviewWidget',
    'app.filament.widgets.recent-activities-widget' => 'App\\Filament\\Widgets\\RecentActivitiesWidget',
    'app.filament.widgets.stock-overview-widget' => 'App\\Filament\\Widgets\\StockOverviewWidget',
    'app.filament.widgets.stock-trends-chart' => 'App\\Filament\\Widgets\\StockTrendsChart',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.auth.pages.edit-profile' => 'Filament\\Auth\\Pages\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.livewire.sidebar' => 'Filament\\Livewire\\Sidebar',
    'filament.livewire.simple-user-menu' => 'Filament\\Livewire\\SimpleUserMenu',
    'filament.livewire.topbar' => 'Filament\\Livewire\\Topbar',
    'filament.auth.pages.login' => 'Filament\\Auth\\Pages\\Login',
    'app.filament.resources.user-resource.pages.list-users' => 'App\\Filament\\Resources\\UserResource\\Pages\\ListUsers',
    'app.filament.resources.user-resource.pages.create-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\CreateUser',
    'app.filament.resources.user-resource.pages.view-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\ViewUser',
    'app.filament.resources.user-resource.pages.edit-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\EditUser',
    'app.filament.resources.outlet-resource.pages.list-outlets' => 'App\\Filament\\Resources\\OutletResource\\Pages\\ListOutlets',
    'app.filament.resources.outlet-resource.pages.create-outlet' => 'App\\Filament\\Resources\\OutletResource\\Pages\\CreateOutlet',
    'app.filament.resources.outlet-resource.pages.view-outlet' => 'App\\Filament\\Resources\\OutletResource\\Pages\\ViewOutlet',
    'app.filament.resources.outlet-resource.pages.edit-outlet' => 'App\\Filament\\Resources\\OutletResource\\Pages\\EditOutlet',
    'app.filament.resources.products.pages.list-products' => 'App\\Filament\\Resources\\Products\\Pages\\ListProducts',
    'app.filament.resources.products.pages.create-product' => 'App\\Filament\\Resources\\Products\\Pages\\CreateProduct',
    'app.filament.resources.products.pages.edit-product' => 'App\\Filament\\Resources\\Products\\Pages\\EditProduct',
    'app.filament.resources.outlet-product-resource.pages.list-outlet-products' => 'App\\Filament\\Resources\\OutletProductResource\\Pages\\ListOutletProducts',
    'app.filament.resources.outlet-product-resource.pages.create-outlet-product' => 'App\\Filament\\Resources\\OutletProductResource\\Pages\\CreateOutletProduct',
    'app.filament.resources.outlet-product-resource.pages.view-outlet-product' => 'App\\Filament\\Resources\\OutletProductResource\\Pages\\ViewOutletProduct',
    'app.filament.resources.outlet-product-resource.pages.edit-outlet-product' => 'App\\Filament\\Resources\\OutletProductResource\\Pages\\EditOutletProduct',
    'app.filament.resources.report-stocks.pages.list-report-stocks' => 'App\\Filament\\Resources\\ReportStocks\\Pages\\ListReportStocks',
    'app.filament.resources.report-stocks.pages.outlet-selection-by-date' => 'App\\Filament\\Resources\\ReportStocks\\Pages\\OutletSelectionByDate',
    'app.filament.resources.report-stocks.pages.stock-details' => 'App\\Filament\\Resources\\ReportStocks\\Pages\\StockDetails',
    'app.filament.resources.report-stocks.pages.create-report-stock' => 'App\\Filament\\Resources\\ReportStocks\\Pages\\CreateReportStock',
    'app.filament.resources.report-stocks.pages.edit-report-stock' => 'App\\Filament\\Resources\\ReportStocks\\Pages\\EditReportStock',
    'app.filament.resources.report-stocks.pages.outlet-selection' => 'App\\Filament\\Resources\\ReportStocks\\Pages\\OutletSelection',
    'app.filament.resources.report-stocks.pages.date-selection' => 'App\\Filament\\Resources\\ReportStocks\\Pages\\DateSelection',
    'app.filament.resources.purchase-requests.pages.list-purchase-requests' => 'App\\Filament\\Resources\\PurchaseRequests\\Pages\\ListPurchaseRequests',
    'app.filament.resources.purchase-requests.pages.purchase-request-dashboard' => 'App\\Filament\\Resources\\PurchaseRequests\\Pages\\PurchaseRequestDashboard',
    'app.filament.resources.purchase-requests.pages.create-purchase-request' => 'App\\Filament\\Resources\\PurchaseRequests\\Pages\\CreatePurchaseRequest',
    'app.filament.resources.purchase-requests.pages.view-purchase-request' => 'App\\Filament\\Resources\\PurchaseRequests\\Pages\\ViewPurchaseRequest',
    'app.filament.resources.purchase-requests.pages.edit-purchase-request' => 'App\\Filament\\Resources\\PurchaseRequests\\Pages\\EditPurchaseRequest',
    'app.filament.resources.purchase-requests.relation-managers.purchase-request-lines-relation-manager' => 'App\\Filament\\Resources\\PurchaseRequests\\RelationManagers\\PurchaseRequestLinesRelationManager',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\apotek\\app\\Filament\\Pages\\PurchaseRequestReports.php' => 'App\\Filament\\Pages\\PurchaseRequestReports',
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\apotek\\app\\Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    0 => 'App\\Filament\\Resources\\UserResource\\UserResource',
    1 => 'App\\Filament\\Resources\\OutletResource\\OutletResource',
    2 => 'App\\Filament\\Resources\\Products\\ProductResource',
    3 => 'App\\Filament\\Resources\\OutletProductResource\\OutletProductResource',
    4 => 'App\\Filament\\Resources\\ReportStocks\\ReportStockResource',
    5 => 'App\\Filament\\Resources\\PurchaseRequests\\PurchaseRequestResource',
  ),
  'resourceDirectories' => 
  array (
  ),
  'resourceNamespaces' => 
  array (
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\apotek\\app\\Filament\\Widgets\\OutletComparisonChart.php' => 'App\\Filament\\Widgets\\OutletComparisonChart',
    'D:\\laragon\\www\\apotek\\app\\Filament\\Widgets\\ParetoAnalysisChart.php' => 'App\\Filament\\Widgets\\ParetoAnalysisChart',
    'D:\\laragon\\www\\apotek\\app\\Filament\\Widgets\\PurchaseRequestTrendsChart.php' => 'App\\Filament\\Widgets\\PurchaseRequestTrendsChart',
    'D:\\laragon\\www\\apotek\\app\\Filament\\Widgets\\PurchaseRequestsOverviewWidget.php' => 'App\\Filament\\Widgets\\PurchaseRequestsOverviewWidget',
    'D:\\laragon\\www\\apotek\\app\\Filament\\Widgets\\RecentActivitiesWidget.php' => 'App\\Filament\\Widgets\\RecentActivitiesWidget',
    'D:\\laragon\\www\\apotek\\app\\Filament\\Widgets\\StockOverviewWidget.php' => 'App\\Filament\\Widgets\\StockOverviewWidget',
    'D:\\laragon\\www\\apotek\\app\\Filament\\Widgets\\StockTrendsChart.php' => 'App\\Filament\\Widgets\\StockTrendsChart',
    0 => 'Filament\\Widgets\\AccountWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\apotek\\app\\Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);