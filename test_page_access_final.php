<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING PAGE ACCESS ===\n\n";

// Test 1: Check if routes exist
echo "1. Testing routes:\n";
try {
    $outletUrl = route('filament.admin.resources.report-stocks.outlets', ['date' => '2025-09-16']);
    echo "   ✅ Outlet URL: {$outletUrl}\n";
} catch (Exception $e) {
    echo "   ❌ Outlet route error: {$e->getMessage()}\n";
}

try {
    $detailUrl = route('filament.admin.resources.report-stocks.details', ['outlet' => 32, 'date' => '2025-09-16']);
    echo "   ✅ Detail URL: {$detailUrl}\n";
} catch (Exception $e) {
    echo "   ❌ Detail route error: {$e->getMessage()}\n";
}

// Test 2: Check if page classes can be instantiated
echo "\n2. Testing page classes:\n";

try {
    $outletPage = new App\Filament\Resources\ReportStocks\Pages\OutletSelectionByDate();
    echo "   ✅ OutletSelectionByDate class instantiated\n";
} catch (Exception $e) {
    echo "   ❌ OutletSelectionByDate error: {$e->getMessage()}\n";
}

try {
    $detailPage = new App\Filament\Resources\ReportStocks\Pages\StockDetails();
    echo "   ✅ StockDetails class instantiated\n";
} catch (Exception $e) {
    echo "   ❌ StockDetails error: {$e->getMessage()}\n";
}

// Test 3: Check if views exist
echo "\n3. Testing view files:\n";

$views = [
    'filament.resources.report-stocks.pages.outlet-selection-by-date',
    'filament.resources.report-stocks.pages.stock-details'
];

foreach ($views as $view) {
    try {
        if (view()->exists($view)) {
            echo "   ✅ View exists: {$view}\n";
        } else {
            echo "   ❌ View missing: {$view}\n";
        }
    } catch (Exception $e) {
        echo "   ❌ View error for {$view}: {$e->getMessage()}\n";
    }
}

// Test 4: Test data availability
echo "\n4. Testing data availability:\n";

$reportStock = App\Models\ReportStock::where('report_date', '2025-09-16')->first();
if ($reportStock) {
    echo "   ✅ ReportStock exists for 2025-09-16\n";
    
    $detailsCount = App\Models\ReportStockDetail::where('report_stock_id', $reportStock->id)->count();
    echo "   ✅ {$detailsCount} ReportStockDetails found\n";
    
    $outletsCount = App\Models\Outlet::whereHas('reportStockDetails', function ($query) use ($reportStock) {
        $query->where('report_stock_id', $reportStock->id);
    })->count();
    echo "   ✅ {$outletsCount} outlets have data\n";
} else {
    echo "   ❌ No ReportStock found for 2025-09-16\n";
}

// Test 5: Test table query
echo "\n5. Testing table queries:\n";

try {
    $reportDate = \Carbon\Carbon::parse('2025-09-16');
    
    $outletQuery = App\Models\Outlet::query()
        ->whereHas('reportStockDetails', function ($query) use ($reportDate) {
            $query->whereHas('reportStock', function ($q) use ($reportDate) {
                $q->where('report_date', $reportDate);
            });
        })
        ->orderBy('name');
    
    $outletCount = $outletQuery->count();
    echo "   ✅ Outlet query returns {$outletCount} results\n";
    
    if ($outletCount > 0) {
        $firstOutlet = $outletQuery->first();
        echo "   ✅ First outlet: {$firstOutlet->name} ({$firstOutlet->code})\n";
        
        // Test detail query for first outlet
        $detailQuery = App\Models\ReportStockDetail::query()
            ->where('outlet_id', $firstOutlet->id)
            ->whereHas('reportStock', function ($q) use ($reportDate) {
                $q->where('report_date', $reportDate);
            })
            ->with(['product', 'product.outlets' => function ($q) use ($firstOutlet) {
                $q->where('outlet_id', $firstOutlet->id);
            }]);
        
        $detailCount = $detailQuery->count();
        echo "   ✅ Detail query for {$firstOutlet->name} returns {$detailCount} results\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Query error: {$e->getMessage()}\n";
}

echo "\n=== SUMMARY ===\n";
echo "✅ All components should be working now\n";
echo "✅ Custom views are created\n";
echo "✅ Data is available\n";
echo "✅ Queries are functional\n";
echo "\nTry accessing the page again in browser!\n";
